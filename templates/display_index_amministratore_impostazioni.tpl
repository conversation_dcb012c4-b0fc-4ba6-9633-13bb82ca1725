{include file="header_amministratore.tpl"}
 <style>
    .popupDialog popupDialog-button{
        width:80%;
    }
  </style>
<script type="text/javascript">
    $(window).on("load", function() {
        $('#messaggio_attesa').hide();
        $('#classi_succ_completate').hide();

        for(var year = (new Date).getFullYear() - 10; year <= (new Date).getFullYear(); year++) {
            setCampiStorici(year+'_'+(year+1));
        }

        $('[id^=messaggio_]').hide();

        $('.vc').height( $('#vc').height() );
        $('.opv').height( $('#opv').height() );
        $('.rpv').height( $('#rpv').height() );
        $('.smv').height( $('#smv').height() );


        $('.collapse_header').click(function(){
            $(this).nextUntil('tr.collapse_header').slideToggle(50);
        });
    });

    function setCampiStorici(year) {
        var enabled = $('[name="abilita_parametri['+ year +'][ABILITA_VISUALIZZAZIONE_STORICA_PORTALE]"]').is(':checked');

        $.each($('[name^="parametri['+ year +']"]'), function(i, e){
            if (enabled) {
                $(e).removeAttr('disabled');
            } else {
                $(e).attr('disabled','disabled');
            }
        });

        $.each($('[name^="bottone['+ year +']"]'), function(i, e){
            if (enabled) {
                $(e).removeAttr('disabled');
            } else {
                $(e).attr('disabled','disabled');
            }
        });
    }

    function openPopup(type, year, name) {
        var field = $('[name="popup['+ year +']['+ name +']"]');

        $('#messaggio_'+ type +'_'+ year).dialog({
            width:540,
            position: { my: "center", at: "center", of: $('[name="bottone['+ year +']['+ name +']"]') },
            buttons: {
                "Ok": function() {
                    $('[name="parametri['+ year +']['+ name +']"]').val(field.val());
                    $(this).dialog("close");
                },
                "Annulla": function() {
                    $(this).dialog("close");
                }
            }
        });

        field.val($('[name="parametri['+ year +']['+ name +']"]').val());
    }

    function show_message() {
        $('#messaggio_attesa').show();
    }

    function show_table(table) {
        if (table === 'view_table_abbinati') {
            $('#classi_succ_completate').show();
            $('#classi_succ_da_completare').hide();
        } else {
            $('#classi_succ_da_completare').show();
            $('#classi_succ_completate').hide();
        }
    }

    function seleziona_tutti(obj) {
        var cbs = document.getElementsByTagName('input');
        for(var i=0; i < cbs.length; i++) {
            if(cbs[i].type == 'checkbox') {
                cbs[i].checked = obj.checked;
            }
        }
    }

    function genera_credenziali_parente(id)
    {
        var data = [
            'current_user='+ $('#current_user').val(),
            'current_key='+ $('#current_key').val(),
            'form_tipo_utente=amministratore',
            'form_azione=genera_credenziali_parente'
        ];

        $.ajax({
            url: 'ajat_manager.php?'+ data.join('&'),
            async: false,
            method: 'POST',
            success: function (res)
            {
                if (typeof (res) !== 'object') {
                    return false;
                }

                if (typeof res.utente !== 'undefined') {
                    $('#parente_'+ id +'_utente').val(res.utente);
                    $('#parente_'+ id +'_codice_attivazione').val(res.codice_attivazione);
                    $('#parente_'+ id +'_commento').show();
                } else {
                    alert("Problemi nella rigenerazione delle credenziali, riprovare più tardi");
                }
            },
            error: function () {
                alert("Problemi nella rigenerazione delle credenziali, riprovare più tardi");
            }
        });
    }

    function checkByClass(className)
    {
        $("."+className).each(function(i) {
            if (this.disabled !== true)
            {
                this.checked = !this.checked;
            }
        });
    }

    function updateOrarioPubblicazioneVotiPortale(selectedValue, idElementToReset)
    {
        let elementToReset = document.getElementById(idElementToReset);

        if (selectedValue != '') {
            elementToReset.value = 0;
            elementToReset.readOnly = true;
        }
        else {
            elementToReset.readOnly = false;
        }
    }

    function confermaCambioParametroMinistero(select){
        const value = select.value;
        if (value == 'NO'){
            if (!confirm("ATTENZIONE: stai per disabilitare le comunicazioni che il Ministero dell'Istruzione e del Merito invia tramite MasterCom.\nQuesta opzione è pensata esclusivamente per le scuole operanti al di fuori del territorio italiano.\nTi invitiamo a contattare preventivamente il Ministero dell'Istruzione e del Merito prima di procedere.\n\nConfermi di voler disattivare tali comunicazioni?")){
                select.value = 'SI';
            } else {
                select.value = 'NO';
            }
        }
    }
</script>

<script type="text/javascript" language="javascript" src="javascript/display_index_amministratore_impostazioni.js?v={$js_version}"></script>
<br>
<table width='100%' class="sfondo_base_generico bordo_generico_rilievo">
    <tr>
        <td align='center' class='setup_area_titolo'>
            {mastercom_label}Gestione Impostazioni:{/mastercom_label}
        </td>
    </tr>
    <tr><td><br></td></tr>
    <tr>
        <td class="padding_cella_generica">
            {if $stato_secondario == 'importazione_orario'}
                {include file="importazione_orario.tpl"}
            {/if}

            {* {{{ cambio anno *}
            {if $stato_secondario == 'cambio_anno'}
                {if $cambio_anno_in_corso}
                    {if $superutente_int == 1}
                        <div align='center' class="titolo_funzione sfondo_contrasto_generico">
                            -- MANUTENZIONE --
                        </div>
                        <div align='center'>
                            Da utilizzare solo in caso il cambio anno sia bloccato per molto tempo e si sia certi non stia girando.
                            <br>
                            <form method='post'>
                                <input type='button' value='Sblocca cambio anno' class="margin8"
                                    onclick="
                                        if (confirm('Procedere allo sblocco?')){
                                            this.form.submit();
                                        }
                                    ">
                                <input type='hidden' name='form_stato' value='amministratore'>
                                <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                <input type='hidden' name='stato_secondario' value='manutenzione_cambio_anno'>
                                <input type='hidden' name='operazione' value='sblocca_cambio_anno'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </div>
                    {/if}
                <p style="text-align: center; font-weight: bold; color:red; font-size: 18px;">{$cambio_anno_in_corso}</p>
                <p style="text-align: center; font-weight: bold; color:red; font-size: 18px;">Cliccare "Ricarica" per controllare il completamento dell'operazione di cambio anno. </p>
                <p style="text-align: center; font-weight: bold; color:red; font-size: 16px;">(Nel caso l'operazione si prolunghi oltre i 30 minuti contattare l'Assistenza. {$message_cambio_anno_iniziato})</p>
                <div style="text-align: center">
                    <form method='post' action='{$SCRIPT_NAME}' name='form_cambio_anno_update'>
                        <input type='button' value='Ricarica' onclick="this.form.submit();">
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                        <input type='hidden' name='stato_secondario' value='cambio_anno'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </div>
                <br>
                {/if}

                {if $cambio_anno_effettuato}
                    {if $superutente_int == 'SI' && $db_key == $db_official}
                    <table style="width: 80%; border-collapse: separate;" align="center">
                        <tr>
                            <td align='center' class="titolo_funzione sfondo_contrasto_generico" colspan='2'>
                                -- MANUTENZIONE --
                            </td>
                        </tr>
                        <tr>
                            <td align='center' colspan="2" class="padding8 sfondo_rosso_op20 bordrad5">
                                {if $annullamento_cambio_anno_in_corso == 'SI'}
                                    <h1 class="testo_rosso">Annullamento cambio anno in corso</h1>
                                    <p>Entro 1 minuto si avvierà la procedura di annullamento cambio anno</p>
                                    <form method='post'>
                                        <input type='submit' value='BLOCCA Annulla cambio anno' class="margin8 testo_rosso bold">
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='manutenzione_cambio_anno'>
                                        <input type='hidden' name='operazione' value='annulla_annulla_cambio_anno'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                    <p>La pagina verrà ricaricata automaticamente tra</p>
                                    <h2 id="timer"></h2>
                                    <form method='post' id="form_to_reload">
                                        <input type='submit' value='Forza ricaricamento' class="margin8">
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='cambio_anno'>
                                        <input type='hidden' name='operazione' value=''>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                    <script>
                                        var tempoRicaricamento = 75; // secondi = 1 minuto e mezzo

                                        // ricarico dopo un minuto e mezzo
                                        setTimeout(function() {
                                            document.getElementById('form_to_reload').submit();
                                        }, tempoRicaricamento*1000);

                                        // timer per l'annullamento da 1 minuto e mezzo
                                        var timer = tempoRicaricamento;
                                        setInterval(function() {
                                            timer--;
                                            if (timer >= 0){
                                                document.getElementById('timer').innerHTML = timer + ' secondi';
                                            } else {
                                                document.getElementById('timer').innerHTML = 'Ricarico..';
                                            }
                                        }, 1000);

                                    </script>
                                {else}
                                    <b>ATTENZIONE!</b>
                                    <br>Eliminare il database SOLO SE e' la prima volta che viene lanciato il cambio anno e si vuole totalmente rimuovere tutti i dati del nuovo anno
                                    <br>Se l'anno era gia' stato aperto tipicamente non si deve eliminare il database
                                    <br><b>NB</b>: con l'annullamento il database dell'anno precedente viene impostato come database UFFICIALE
                                    <form method='post'>
                                        <input type='button' value='Annulla cambio anno' class="margin8 testo_rosso bold"
                                            onclick="
                                                if (confirm('Procedere all\'annullamento?')){
                                                    this.form.submit();
                                                }
                                            ">
                                        <input type="hidden" name="elimina_db" value="NO">
                                        <label class="bold"><input type="checkbox" name="elimina_db" value="SI">Elimina anche il database</label>
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='manutenzione_cambio_anno'>
                                        <input type='hidden' name='operazione' value='annulla_cambio_anno'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                {/if}
                            </td>
                        </tr>
                    </table>
                    <hr>
                    {/if}
                    <form method='post' align="center">
                        <table style="width: 80%; border-collapse: separate;" align="center">
                            <tr>
                                <td colspan="2" style="font-weight: bold; text-align: center;">Gestione studenti <br> con Giudizio Sospeso nell'A.S. precedente con esito definito</td>
                                <td colspan="3" align="center">
                                    <b>Abbina tutti gli studenti selezionati alla classe</b><br>
                                    {mastercom_auto_select
                                    name="classe_totale"
                                    array_dati=$elenco_classi
                                    optgroups='y'
                                    firstempy='y'}
                                    {/mastercom_auto_select}

                                    <input type='submit' value='Salva'>
                                </td>
                            </tr>
                            <tr><td></td></tr>
                            <tr class='sottotitolo_funzione sfondo_scuro_generico'>
                                    <td class="padding_cella_generica" width="10%" colspan="1">Seleziona tutti<br><input type="checkbox" name="toggle_select" onclick="seleziona_tutti(this);">
                                </td>
                                <td class="padding_cella_generica" width="10%" colspan="1">N.
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Alunno
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Classe A.S. precedente
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Esito A.S. precedente
                                </td>
                            </tr>
                            {if !empty($elenco_alunni)}
                                {foreach $elenco_alunni as $alunno}
                                    <tr class='highlight {cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                        <td class="padding_cella_generica" colspan="1">
                                            <input type="checkbox" name="studenti_selezionati[]" value="{$alunno.id_studente}">
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno@iteration}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno.cognome} {$alunno.nome}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno.classe} {$alunno.sezione} - {$alunno.indirizzo}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno.esito}
                                        </td>
                                    </tr>
                                {/foreach}
                            {else}
                                <tr>
                                    <td colspan="5" style="font-weight: bold; font-size: 14px;">
                                        Tutti gli studenti sono abbinati al prossimo anno scolastico
                                    </td>
                                </tr>
                            {/if}
                        </table>
                        <input type='hidden' name='form_stato' value='amministratore'>
                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                        <input type='hidden' name='stato_secondario' value='cambio_anno'>
                        <input type='hidden' name='operazione' value='salva_abbinamento_successivo_sospesi'>
                        <input type='hidden' name='id_classe' value='{$dati_classe.id_classe}'>
                        <input type='hidden' name='id_studente' value=''>
                        <input type='hidden' name='visualizzazione' value='{$operazione}'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>

                    <br><br><hr><br>

                    <form method='post' align="center">
                        <table style="width: 80%; border-collapse: separate;" align="center">
                            <tr>
                                <td colspan="2" style="font-weight: bold; text-align: center;">Gestione studenti <br> che hanno frequentato lo scorso A.S. all'estero</td>
                                <td colspan="3" align="center">
                                    <b>Abbina tutti gli studenti selezionati alla classe</b><br>
                                    {mastercom_auto_select
                                    name="classe_totale"
                                    array_dati=$elenco_classi
                                    optgroups='y'
                                    firstempy='y'}
                                    {/mastercom_auto_select}

                                    <input type='submit' value='Salva'>
                                </td>
                            </tr>
                            <tr><td></td></tr>
                            <tr class='sottotitolo_funzione sfondo_scuro_generico'>
                                    <td class="padding_cella_generica" width="10%" colspan="1">Seleziona tutti<br><input type="checkbox" name="toggle_select" onclick="seleziona_tutti(this);">
                                </td>
                                <td class="padding_cella_generica" width="10%" colspan="1">N.
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Alunno
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Classe A.S. precedente
                                </td>
                                <td class="padding_cella_generica" width="30%" colspan="1">Esito da impostare
                                </td>
                            </tr>
                            {if !empty($elenco_alunni_estero)}
                                {foreach $elenco_alunni_estero as $alunno}
                                    <tr class='highlight {cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                        <td class="padding_cella_generica" colspan="1">
                                            <input type="checkbox" name="studenti_selezionati[]" value="{$alunno.id_studente}">
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno@iteration}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno.cognome} {$alunno.nome}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            {$alunno.classe} {$alunno.sezione} - {$alunno.indirizzo}
                                        </td>
                                        <td class="padding_cella_generica" colspan="1">
                                            <select name="nuovo_esito_estero[{$alunno.id_studente}]">
                                                <option value="">Selezionare l'esito</option>
                                                <option value="Ammesso alla classe successiva">Ammesso/a alla classe successiva</option>
                                                <option value="Non ammesso alla classe successiva">Non ammesso/a alla classe successiva</option>
                                            </select>
                                        </td>
                                    </tr>
                                {/foreach}
                            {else}
                                <tr>
                                    <td colspan="5" style="font-weight: bold; font-size: 14px;">
                                        Tutti gli studenti sono abbinati al prossimo anno scolastico
                                    </td>
                                </tr>
                            {/if}
                        </table>
                        <input type='hidden' name='form_stato' value='amministratore'>
                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                        <input type='hidden' name='stato_secondario' value='cambio_anno'>
                        <input type='hidden' name='operazione' value='salva_abbinamento_successivo_estero'>
                        <input type='hidden' name='id_classe' value='{$dati_classe.id_classe}'>
                        <input type='hidden' name='id_studente' value=''>
                        <input type='hidden' name='visualizzazione' value='{$operazione}'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>


                    <div id="messaggio_attesa">
                    <p style="text-align: center; font-weight: bold; color: blue;">L'abbinamento potrebbe richiedere qualche minuto per il completamento. <br>
                        Si prega di attendere prima di effettuare qualsiasi operazione.</p>
                    </div>
                    <br>
                    <div align="center">
                    <form method='post' align="center">
                        <input type='submit' value='Tenta abbinamento automatico'  onclick="show_message();">
                        <input type='hidden' name='form_stato' value='amministratore'>
                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                        <input type='hidden' name='stato_secondario' value='inserimento_automatico_classi_sospesi'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                    </div>
                    <br><br>
                    <table style="width: 80%; border-collapse: separate;" align="center">
                        <tr>
                                <td colspan="3" style="font-weight: bold; text-align: center;">Elenco studenti <br> con Giudizio Sospeso nell'A.S. precedente ancora non definiti</td>
                            </tr>
                            <tr><td></td></tr>
                        <tr class='sottotitolo_funzione sfondo_scuro_generico'>
                            <td class="padding_cella_generica" width="10%" colspan="1">N.
                            </td>
                            <td class="padding_cella_generica" width="30%" colspan="1">Alunno
                            </td>
                            <td class="padding_cella_generica" width="30%" colspan="1">Classe di provenienza
                            </td>
                        </tr>
                        {if !empty($elenco_studenti_gs)}
                            {foreach $elenco_studenti_gs as $alunno}
                                <tr class='highlight {cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                    <td class="padding_cella_generica" colspan="1">
                                        {$alunno@iteration}
                                    </td>
                                    <td class="padding_cella_generica" colspan="1">
                                        {$alunno.cognome} {$alunno.nome}
                                    </td>
                                    <td class="padding_cella_generica" colspan="1">
                                        {$alunno.classe_provenienza} {$alunno.sezione_provenienza}
                                    </td>
                                </tr>
                            {/foreach}
                        {/if}
                    </table>
                {else}
                <table width='100%'>
                    {if $superutente_int == 'SI' && $db_key == $db_official}
                        <tr>
                            <td align='center' class="titolo_funzione sfondo_contrasto_generico" colspan='2'>
                                -- MANUTENZIONE --
                            </td>
                        </tr>
                        <tr>
                            <td align='center' colspan="2" class="padding8 sfondo_rosso_op20 bordrad5">
                                {if $annullamento_cambio_anno_in_corso == 'SI'}
                                    <h1 class="testo_rosso">Annullamento cambio anno in corso</h1>
                                    <p>Entro 1 minuto si avvierà la procedura di annullamento cambio anno</p>
                                    <form method='post'>
                                        <input type='submit' value='BLOCCA Annulla cambio anno' class="margin8 testo_rosso bold">
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='manutenzione_cambio_anno'>
                                        <input type='hidden' name='operazione' value='annulla_annulla_cambio_anno'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                    <p>La pagina verrà ricaricata automaticamente tra</p>
                                    <h2 id="timer"></h2>
                                    <form method='post' id="form_to_reload">
                                        <input type='submit' value='Forza ricaricamento' class="margin8">
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='cambio_anno'>
                                        <input type='hidden' name='operazione' value=''>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                    <script>
                                        var tempoRicaricamento = 75; // secondi = 1 minuto e mezzo

                                        // ricarico dopo un minuto e mezzo
                                        setTimeout(function() {
                                            document.getElementById('form_to_reload').submit();
                                        }, tempoRicaricamento*1000);

                                        // timer per l'annullamento da 1 minuto e mezzo
                                        var timer = tempoRicaricamento;
                                        setInterval(function() {
                                            timer--;
                                            if (timer >= 0){
                                                document.getElementById('timer').innerHTML = timer + ' secondi';
                                            } else {
                                                document.getElementById('timer').innerHTML = 'Ricarico..';
                                            }
                                        }, 1000);

                                    </script>
                                {else}
                                    <b>ATTENZIONE!</b>
                                    <br>Eliminare il database SOLO SE e' la prima volta che viene lanciato il cambio anno e si vuole totalmente rimuovere tutti i dati del nuovo anno
                                    <br>Se l'anno era gia' stato aperto tipicamente non si deve eliminare il database
                                    <br><b>NB</b>: con l'annullamento il database dell'anno precedente viene impostato come database UFFICIALE
                                    <form method='post'>
                                        <input type='button' value='Annulla cambio anno' class="margin8 testo_rosso bold"
                                            onclick="
                                                if (confirm('Procedere all\'annullamento?')){
                                                    this.form.submit();
                                                }
                                            ">
                                        <input type="hidden" name="elimina_db" value="NO">
                                        <label class="bold"><input type="checkbox" name="elimina_db" value="SI">Elimina anche il database</label>
                                        <input type='hidden' name='form_stato' value='amministratore'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='manutenzione_cambio_anno'>
                                        <input type='hidden' name='operazione' value='annulla_cambio_anno'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                {/if}
                            </td>
                        </tr>
                    {/if}
                    <tr>
                        <td align='center' class="titolo_funzione sfondo_contrasto_generico" colspan='2'>
                            Procedura di Cambio Anno:
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' width='95%'>
                            Prima di procedere con la chiusura dell'anno scolastico &egrave; necessario procedere all'impostazione di tutti i dati necessari.<br>
                            Si prega di consultare il manuale relativo qui riportato per i dettagli.
                        </td>
                        <td width='5%' align='right'>
                            <button onclick="window.open('manuali/manuale_nuovo_cambio_anno.pdf', 'manuale');"><img src="icone/manuale_scuro24.gif"></button>
                        </td>
                    </tr>
                </table>
                <table  width='100%'>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' colspan='2'>
                            <table width='100%'>
                                <tr>
                                    <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                        Legenda:
                                    </td>
                                </tr>
                                <tr>
                                    <td class='divisore_basso' width='60'>
                                        <img src="icone/icona.php?icon=ok&label_bg=44621C&size=48" />
                                    </td>
                                    <td class='divisore_basso'>
                                        Dati impostati correttamente
                                    </td>
                                </tr>
                                <tr>
                                    <td class='divisore_basso'>
                                        <img src="icone/icona.php?icon=forse&label_bg=FFCC00&size=48" />
                                    </td>
                                    <td class='divisore_basso'>
                                        Dati parzialmente inseriti che necessitano di una verifica. <strong>QUESTA SEGNALAZIONE NON IMPEDISCE IL CAMBIO ANNO.</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class='padding_cella_generica'>
                                        <img src="icone/icona.php?icon=ko&label_bg=FF0000&size=48" />
                                    </td>
                                    <td class='padding_cella_generica'>
                                        Dati non inseriti. <strong>QUESTA SEGNALAZIONE BLOCCA IL CAMBIO ANNO.</strong>
                                    </td>
                                </tr>
                            </table>
                            <br>
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2' class='titolo_funzione sfondo_contrasto_generico'>
                            Verifiche effettuate sui dati:
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td colspan='2'>
                            {foreach from=$mat_controlli key=key item=singolo_controllo}
                                {if $singolo_controllo.stato eq 'OK'}
                                    {assign "color" "44621C"}
                                {elseif $singolo_controllo.stato eq 'FORSE'}
                                    {assign "color" "FFCC00"}
                                {elseif $singolo_controllo.stato eq 'KO'}
                                    {assign "color" "FF0000"}
                                {/if}
                                <table width='100%'>
                                    <tr>
                                        <td align='center' height='60' width="60">
                                            <img src="icone/icona.php?icon={$singolo_controllo.stato|lower}&label_bg={$color}&size=48" />
                                        </td>
                                        <td class='contenitore_generico sfondo_contrasto_generico'>
                                            <strong>{$singolo_controllo.cosa}:</strong>
                                            <br><br>
                                            {$singolo_controllo.messaggio}

                                            {if $key == 'classi_indirizzi_succ' && $singolo_controllo.stato == 'KO'}
                                                <br><br>
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        <table style="width: 100%; text-align: center;">
                                                            <tr>
                                                                <td style="width: 20%"></td>
                                                                <td style="width: 20%"></td>
                                                                <td style="border: 1pt solid black; padding: 5px;">
                                                                    <p style="font-weight: bold">Selezionare se importare gli abbinamenti Classi-Professori-Materie</p>
                                                                </td>
                                                                <td style="width: 20%"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 20%"></td>
                                                                <td style="padding: 5px; width: 20%;">
                                                                    <input type='submit' value='Crea Classi e Indirizzi successivi'>
                                                                    <input type='hidden' name='form_stato' value='amministratore'>
                                                                    <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                                                    <input type='hidden' name='stato_secondario' value='gestione_classi_indirizzi_anno_succ_display'>
                                                                    <input type='hidden' name='operazione' value='importa'>
                                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                                </td>
                                                                <td style="border: 1pt solid black; padding: 5px; width: 20%;">
                                                                    {mastercom_auto_select name="form_abbinamenti"}
                                                                        SI###Importare abbinamenti prof.@@@
                                                                        NO###Non importare
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                                <td style="width: 20%"></td>
                                                            </tr>
                                                        </table>
                                                    </form>
                                                </div>
                                            {/if}

                                            {if $key == 'classi_indirizzi_succ' && $singolo_controllo.stato == 'FORSE'}
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        <input type='submit' value='Verifica Classi e Indirizzi successivi'>
                                                        <input type='hidden' name='form_stato' value='amministratore'>
                                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                                        <input type='hidden' name='stato_secondario' value='gestione_classi_indirizzi_anno_succ_display'>
                                                        <input type='hidden' name='operazione' value=''>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </div>
                                            {/if}

                                            {if $key == 'classi_indirizzi_succ' && $singolo_controllo.stato == 'OK'}
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        <input type='button' value='{mastercom_label}Nuova classe{/mastercom_label}' onclick='this.form.id_classe.value = -1;
                                                            this.form.submit();'>
                                                        <input type='button' value='{mastercom_label}Nuovo indirizzo{/mastercom_label}' onclick='this.form.id_indirizzo.value = -1;
                                                            this.form.submit();'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='gestione_classi_indirizzi_anno_succ_display'>
                                                        <input type='hidden' name='id_classe' value=''>
                                                        <input type='hidden' name='operazione' value=''>
                                                        <input type='hidden' name='id_indirizzo' value=''>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </div>
                                            {/if}

                                            {if $key == 'studenti_succ' && $singolo_controllo.stato != 'OK'}
                                                <br><br>

                                                <div id="messaggio_attesa">
                                                    <p style="text-align: center; font-weight: bold; color: blue;">L'abbinamento potrebbe richiedere qualche minuto per il completamento. <br>
                                                        Si prega di attendere prima di effettuare qualsiasi operazione.</p>
                                                </div>
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        {if $singolo_controllo.stato == 'KO'}
                                                            <input type='submit' value='Abbina in automatico gli studenti alle classi successive ove possibile'  onclick="show_message();">
                                                        {elseif $singolo_controllo.stato == 'FORSE'}
                                                            <input type='submit' value='Tenta abbinamento automatico solo per studenti ancora non abbinati'  onclick="show_message();">
                                                        {else}
                                                            Contattare l'assistenza
                                                        {/if}
                                                        <input type='hidden' name='form_stato' value='amministratore'>
                                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                                        <input type='hidden' name='stato_secondario' value='inserimento_automatico_classi_succ'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </div>

                                                <br><br>

                                                <form method='post' align="center">
                                                    <input type='hidden' name='form_stato' value='amministratore'>
                                                    <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                                    <input type='hidden' name='stato_secondario' value='dettaglio_studenti_classi_succ'>
                                                    <input type='hidden' name='visualizzazione' value=''>
                                                    <input type='hidden' name='id_classe' value=''>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>

                                                    <table id="classi_succ_completate" style="width: 100%; text-align: center; border-collapse: separate;">
                                                        <tr class="sottotitolo_funzione sfondo_scuro_generico">
                                                            <td class="padding_cella_generica">
                                                                Classe attuale
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Indirizzo
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti presenti <br> nella classe
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti abbinati <br> al prossimo anno scolastico
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti da abbinare <br> al prossimo anno scolastico
                                                            </td>
                                                        </tr>
                                                        {foreach $classi_indirizzi_anno_succ as $key => $classi}
                                                            {foreach $classi as $classe}
                                                                <tr class='highlight {cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.classe} {$classe.sezione}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.descrizione_indirizzo}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.numero_studenti_classe}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        <input type="button" name="classe_abbinati_{$classe.id_classe}" value="{$classe.numero_studenti_abbinati}" {if $classe.numero_studenti_abbinati == 0} disabled {/if}
                                                                               style="width: 50px; height: 30px;"
                                                                               onclick="this.form.id_classe.value='{$classe.id_classe}'; this.form.visualizzazione.value='visualizza_dettaglio_studenti_abbinati_classi_succ'; this.form.submit();">
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        <input type="button" name="classe_da_abbinare_{$classe.id_classe}" value="{$classe.numero_studenti_da_abbinare}" {if $classe.numero_studenti_da_abbinare == 0} disabled {/if}
                                                                               style="width: 50px; height: 30px; {if $classe.numero_studenti_da_abbinare > 0} color: red; font-weight: bold; {/if}"
                                                                               onclick="this.form.id_classe.value='{$classe.id_classe}'; this.form.visualizzazione.value='visualizza_dettaglio_studenti_da_abbinare_classi_succ'; this.form.submit();">
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                        {/foreach}
                                                    </table>

                                                    <table id="classi_succ_da_completare" style="width: 100%; text-align: center; border-collapse: separate;">
                                                        <tr class="sottotitolo_funzione sfondo_scuro_generico">
                                                            <td class="padding_cella_generica">
                                                                Classe attuale
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Indirizzo
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti presenti <br> nella classe
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti abbinati <br> al prossimo anno scolastico
                                                            </td>
                                                            <td class="padding_cella_generica">
                                                                Studenti da abbinare <br> al prossimo anno scolastico
                                                            </td>
                                                        </tr>
                                                        {foreach $classi_indirizzi_anno_succ_da_completare as $key => $classi}
                                                            {foreach $classi as $classe}
                                                                <tr class='highlight {cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.classe} {$classe.sezione}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.descrizione_indirizzo}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        {$classe.numero_studenti_classe}
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        <input type="button" name="classe_abbinati_{$classe.id_classe}" value="{$classe.numero_studenti_abbinati}"
                                                                               style="width: 50px; height: 30px;"
                                                                               onclick="this.form.id_classe.value='{$classe.id_classe}'; this.form.visualizzazione.value='visualizza_dettaglio_studenti_abbinati_classi_succ'; this.form.submit();">
                                                                    </td>
                                                                    <td class="padding_cella_generica">
                                                                        <input type="button" name="classe_da_abbinare_{$classe.id_classe}" value="{$classe.numero_studenti_da_abbinare}" {if $classe.numero_studenti_da_abbinare == 0} disabled {/if}
                                                                               style="width: 50px; height: 30px; {if $classe.numero_studenti_da_abbinare > 0} color: red; font-weight: bold; {/if}"
                                                                               onclick="this.form.id_classe.value='{$classe.id_classe}'; this.form.visualizzazione.value='visualizza_dettaglio_studenti_da_abbinare_classi_succ'; this.form.submit();">
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                        {/foreach}
                                                    </table>
                                                </form>
                                            {/if}

                                            {if $key == 'studenti_succ' && $singolo_controllo.stato == 'FORSE'}
                                                <br>
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        <input type='submit' value='Effettua abbinamento manuale degli studenti alle classi successive'>
                                                        <input type='hidden' name='form_stato' value='amministratore'>
                                                        <input type='hidden' name='stato_principale' value='ricerca_full'>
                                                        <input type='hidden' name='stato_secondario' value='ricerca_update'>
                                                        <input type='hidden' name='form_azione_ricerca' value='ABBINAMENTI'>
                                                        <input type='hidden' name='filtro_classe_succ' value='NO'>
                                                        <input type='hidden' name='ordinamento' value='sort_class'>
                                                        <input type='hidden' name='filtro_storico'value='NO'>
                                                        <input type='hidden' name='filtro_non_abbinati'value='NO'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </div>
                                            {/if}

                                            {if $key == 'abbinamenti_succ' && $singolo_controllo.stato == 'FORSE'}
                                                <br>
                                                <script type="text/javascript">
                                                    var form = document.getElementsByName('abbinamenti_succ_form');
                                                    $.each(form, function() {
                                                        this.current_key.value = '{$current_key}';
                                                    });
                                                </script>
                                            {/if}
                                        </td>
                                    </tr>
                                </table>
                                <br>
                            {/foreach}
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2' class='titolo_funzione colore_testo_notifiche'>
                            Sarà possibile procedere col cambio d'anno a partire dal mese di Luglio {$anno_fine} e se nessun controllo ha esito negativo
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2'>
                            <br>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                {if $db_key == $db_official}
                                    <label>
                                        <input type="checkbox" name="finalizza_cambio_anno" value="SI">Finalizza la procedura e passa al nuovo anno scolastico
                                    </label>
                                    <br><br>
                                {/if}
                                {if $found == 'false'}
                                    {literal}
                                        <input type='button' value='Esegui cambio anno' onclick="if (confirm('Procedere con il cambio d\'anno? Attenzione, la procedura non &egrave; reversibile e potrebbe richiedere alcuni minuti.\n\nATTENZIONE: al momento del lancio verrete disconnessi dal sistema, si prega di attendere alcuni mintui prima di riconnettersi!')) {
                                                    this.disabled = true;
                                                    //alert('L\' operazione richieder&agrave; alcuni minuti.');
                                                    this.form.submit();
                                                }">
                                    {/literal}
                                {else}
                                    <input type='button' value='Esegui cambio anno' disabled="disabled">
                                {/if}
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='cambio_anno_update'>
                                <input type='hidden' name='stato_recupero' value='seleziona_anno'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                            <br>
                        </td>
                    </tr>
                </table>
                {/if}
            {/if}

            {if $stato_secondario == 'cambio_anno_update'}
                <table  width='100%'>
                    <tr>
                        <td align='center' class="titolo_funzione sfondo_contrasto_generico" colspan='2'>
                            Procedura di Cambio Anno:
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' width='95%' style='font-size:18px; font-weight: bold;'>
                            Procedura di Cambio Anno in corso.<br>Non ricaricare la pagina
                        </td>
                        <td width='5%' align='right'>
                            <button onclick="window.open('manuali/manuale_nuovo_cambio_anno.pdf', 'manuale');"><img src="icone/manuale_scuro24.gif"></button>
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2'>
                            <br>
                            <form method='post' action='{$SCRIPT_NAME}' name='form_cambio_anno_update'>
                                <input type='button' value='Vai a cambio anno' onclick="this.form.submit();">
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='cambio_anno'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                            <br>
                        </td>
                    </tr>
                </table>
            {/if}
            {* }}} *}

            {if $stato_secondario == "dettaglio_studenti_classi_succ"}
                {include file="impostazioni/include_dettaglio_classi_cambio_anno.tpl"}
            {/if}

            {if $stato_secondario == "dati_base_sedi"}
                {include file="impostazioni/dati_base_sedi.tpl"}
            {/if}

            {if $stato_secondario == "visualizza_anni_precedenti_display"}
                {include file="impostazioni/visualizza_anni_precedenti.tpl"}
            {/if}

            {if $stato_secondario == "setup_tasse"}
                {include file="impostazioni/setup_tasse.tpl"}
            {/if}

            {if $stato_secondario == "gestione_alternanza_classi"}
                {include file="impostazioni/setup_alternanza_classi.tpl"}
            {/if}

            {if $stato_secondario == "parametri_estetici_display"}
                {include file="impostazioni/parametri_estetici.tpl"}
            {/if}

            {if $stato_secondario == "gestione_sms_display" or $stato_secondario == "intestazione_sms"}
                {include file="impostazioni/parametri_sms.tpl"}
            {/if}

            {if $stato_secondario == "esportazione_per_crs_lombardia"}
                {include file="impostazioni/esportazione_per_crs_lombardia.tpl"}
            {/if}

            {if $stato_secondario == "integrazioni_esterne"}
                <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                <table width='100%' align='center'>
                    <tr>
                        <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                            Integrazioni esterne
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='right' width="50%">
                            Tipologia:
                        </td>
                        <td>
                            <SELECT name="stato_export">
                                <OPTION selected value="microsoft">Microsoft</OPTION>
                                <OPTION value="apple">Apple</OPTION>
                            </SELECT>
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' colspan="2" class='padding_cella_generica'>
                            Selezionare le classi:
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2'>
                            {mastercom_grid_classes
                                mat_classi=$elenco_classi_accessibili_generale
                                mat_checks=$mat_classi
                                onclick_submit='NO'
                                default_background='y'
                                onclick_set_hidden_id='id_classe'
                                onclick_set_hidden_name='classe'
                                status_light ='no'
                                checks_active='si'
                                ver2_label_bottone='label'
                                bold_text='y'
                                only_main='y'
                            }
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' colspan='2'>
                            <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                        </td>
                    </tr>
              </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='integrazioni_esterne_export'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {/if}

            {if $stato_secondario == "generazione_password_display"}
                {* {{{ generazione password display *}
                <br>
                <table width='100%' align='center'>
                    {if $superutente_int == "SI"}
                        <tr>
                            <td colspan="4" align='center' class="sottotitolo_testo_bold sfondo_contrasto_generico">
                                Rigenerazione utente e password Studenti
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td colspan='4' align="center">
                                Proseguendo verranno generate nuovamente tutte le password e i codici di tutti gli studenti dell'istituto, proseguire?
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td width="40%" align="right" style="font-size:14px; font-weight:bold">STUDENTI:</td>
                            <td width="10%">
                                <form method='post' action='{$SCRIPT_NAME}'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                    <input type='hidden' name='stato_secondario' value='generazione_password_update'>
                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                </form>
                            </td>
                            <td width="10%" align="right" style="font-size:14px; font-weight:bold">GENITORI:</td>
                            <td width="40%">
                                <form method='post' action='{$SCRIPT_NAME}'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                    <input type='hidden' name='stato_secondario' value='generazione_password_genitori_update'>
                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                </form>
                            </td>
                        </tr>
                    {else}
                        <tr>
                            <td colspan='2' class='titolo_testo'>
                                >Essendo un'operazione molto delicata che potrebbe recare danno alla scuola e per evitare utilizzi involontari di questa funzionalità, si prega di contattare il nostro servizio di assistenza, il quale provvederà immediatamente ad effettuare la rigenerazione delle password di tutto l'istituto secondo la Vostra esigenza.</font>
                            </td>
                        </tr>
                    {/if}
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_password_docenti_display"}
                {* {{{ generazione password display *}
                <br>
                <form method='post' action='{$SCRIPT_NAME}'>
                    <table width='100%' align='center' style="border-collapse: separate;">
                        <tr>
                            <td colspan="4" align='center' class="sottotitolo_testo_bold sfondo_contrasto_generico">
                                Rigenerazione password Docenti
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td align='center' colspan='3'>
                                Proseguendo verranno resettate tutte le password di tutti i docenti dell'istituto, impostandole secondo la struttura sotto indicata.
                                <br>Questa password sarà valida solo fino al primo login, quando al docente verr&agrave; chiesto di scegliersi una password personale. Proseguire?
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td align='center' colspan='3'>
                                {mastercom_auto_select name="sovrascrivi_pwd"}
                                SOVRASCRIVI_NON_CAMBIATE###Sovrascrivi solo le password dei docenti che non hanno gia cambiato la propria password@@@
                                SOVRASCRIVI_TUTTE###Sovrascrivi tutte le password di tutti i docenti
                                {/mastercom_auto_select}
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr valign="middle">
                            <td align='right'>
                                Prefisso<br>
                                <input type='text' name='prefisso_pwd' size='6' value={$prefisso_password_criptate}>
                            </td>
                            <td align='center' width='10%'><br>
                                &lt;nome_utente&gt;
                            </td>
                            <td>
                                Suffisso<br>
                                <input type='text' name='suffisso_pwd' size='6' value={$suffisso_password_criptate}>
                            </td>
                        </tr>
                        <tr>
                            <td align='center' colspan='3'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='generazione_password_docenti_update'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </td>
                        </tr>
                    </table>
                </form>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_password_update"}
                {* {{{ generazione password update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            Generazione password e codici studenti ultimata con successo.
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_codici_totali_display"}
                {* {{{ generazione badge display *}
                <br>
                <form method='post' action='{$SCRIPT_NAME}'>
                    <table width='100%' align='center'>
                        {if $superutente_int == "SI"}
                            <tr>
                                <td colspan='2' align='center' class="titolo_funzione sfondo_contrasto_generico">
                                    Generazione Totale Codici
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td colspan='2' class='padding_cella_generica'>
                                    Proseguendo verranno generati e sovrascritti tutti i badge e/o le password e/o numeri di registro e/o numeri di matricola di tutti gli studenti delle classi selezionate, proseguire?
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr class="sfondo_contrasto_generico">
                                <td align='right' width="50%" class='padding_cella_generica'>
                                    Badge
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="crea_badge">
                                        <OPTION selected value="NO">Non ricrearli</OPTION>
                                        <OPTION value="SI TUTTI">Ricrearli di tutti gli studenti(sovrascrivere gli esistenti)</OPTION>
                                        <OPTION value="SI VUOTI">Inserire solo quelli vuoti degli studenti(='0')</OPTION>
                                        <OPTION value="SI SOLO STAMPA BADGE">Ricreare tutti quelli che hanno stampa_badge ='SI'</OPTION>
                                        <OPTION value="SI TUTTI ATA">Ricrearli di tutto il personale ATA(sovrascrivere gli esistenti)</OPTION>
                                        <OPTION value="SI VUOTI ATA">Inserire solo quelli vuoti del personale ATA(='0')</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Utenti e password studenti
                                </td
                                ><td class='padding_cella_generica'>
                                    <SELECT name="crea_password">
                                        <OPTION selected value="NO">Non ricrearli</OPTION>
                                        <OPTION value="SI TUTTI">Ricrearli tutti (sovrascrivere gli esistenti)</OPTION>
                                        <OPTION value="SI VUOTI">Inserire solo quelli vuoti(='')</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr class="sfondo_contrasto_generico">
                                <td align='right' class='padding_cella_generica'>
                                    Utenti e password genitori
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="crea_password_genitori">
                                        <OPTION selected value="NO">Non ricrearli</OPTION>
                                        <OPTION value="SI TUTTI">Ricrearli tutti (sovrascrivere gli esistenti)</OPTION>
                                        <OPTION value="SI VUOTI">Inserire solo quelli vuoti(='')</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr>
                                <td align='right' class='padding_cella_generica'>
                                    Numeri di registro
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="crea_num_registro">
                                        <OPTION selected value="NO">Non ricrearli</OPTION>
                                        <OPTION value="SI TUTTI">Ricrearli tutti (sovrascrivere gli esistenti)</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr class="sfondo_contrasto_generico">
                                <td align='right' class='padding_cella_generica'>
                                    Numeri di matricola
                                </td>
                                <td class='padding_cella_generica'>
                                    <SELECT name="crea_num_matricola">
                                        <OPTION selected value="NO">Non ricrearli</OPTION>
                                        <OPTION value="SI TUTTI">Ricrearli tutti (sovrascrivere gli esistenti)</OPTION>
                                        <OPTION value="SI VUOTI">Inserire solo quelli vuoti degli studenti(='0')</OPTION>
                                    </SELECT>
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            <tr>
                                <td align='right' colspan="2" class="bordo_tabella_generico">
                                    {mastercom_grid_classes
                                        mat_classi=$elenco_classi_accessibili_generale
                                        mat_checks=$mat_checks
                                        onclick_submit='NO'
                                        default_background='y'
                                        onclick_set_hidden_id='id_classe'
                                        onclick_set_hidden_name='classe'
                                        status_light ='no'
                                        checks_active='si'
                                        ver2_label_bottone='label'
                                        only_main='y'
                                        bold_text='y'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td align='center' colspan="2">
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                    <input type='hidden' name='stato_secondario' value='generazione_codici_totali_update'>
                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                </td>
                            </tr>
                        {else}
                            <tr>
                                <td colspan='2' class='titolo_testo'>
                                    Essendo un'operazione molto delicata che potrebbe recare danno alla scuola e per evitare utilizzi involontari di questa funzionalità, si prega di contattare il nostro servizio di assistenza, il quale provvederà immediatamente ad effettuare la rigenerazione delle password di tutto l'istituto secondo la Vostra esigenza.
                                </td>
                            </tr>
                        {/if}
                    </table>
                </form>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_codici_totali_update"}
                {* {{{ generazione codici totali update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            Generazione ultimata con successo.
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "recupero_studenti_storici_display"}
                {include file="impostazioni/recupero_studenti_storici.tpl"}
            {/if}

            {if $stato_secondario == "gestione_campi_liberi"}
                {include file="impostazioni/gestione_campi_liberi.tpl"}
            {/if}

            {if $stato_secondario == "generazione_campi_liberi_display"}
                {include file="impostazioni/generazione_campi_liberi.tpl"}
            {/if}

            {if $stato_secondario == "gestione_abbinamenti_sostegno"}
                {include file="impostazioni/gestione_abbinamenti_sostegno.tpl"}
            {/if}

            {if $stato_secondario == "generazione_registro_display"}
                {* {{{ generazione numeri di registro display *}
                <br>
                <table width='100%' align='center'>
                    {if $superutente_int == "SI"}
                        <tr>
                            <td colspan="3" align='center' class="sottotitolo_testo_bold sfondo_contrasto_generico">
                                Rigenerazione completa numeri di registro
                            </td>
                        </tr>
                        <tr><td><br></td></tr>
                        <tr>
                            <td width="33%">

                            </td>
                            <td>
                                Proseguendo verranno generati tutti i numeri di registro di ogni classe dell'istituto. Proseguire?
                            </td>
                            <td width="33%">
                            </td>
                        </tr>
                        <tr>
                            <td align='center' colspan="3">
                                <form method='post' action='{$SCRIPT_NAME}'>
                                    <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                    <input type='hidden' name='stato_secondario' value='generazione_registro_update'>
                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                </form>
                            </td>
                        </tr>
                    {else}
                        <tr>
                            <td colspan='2' class='titolo_testo'>
                                Essendo un'operazione molto delicata che potrebbe recare danno alla scuola e per evitare utilizzi involontari di questa funzionalità, si prega di contattare il nostro servizio di assistenza, il quale provvederà immediatamente ad effettuare la rigenerazione dei numeri di registro di tutto l'istituto secondo la Vostra esigenza.
                            </td>
                        </tr>
                    {/if}
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_registro_update"}
                {* {{{ generazione numeri di registro update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            Generazione Numeri di Registro ultimata con successo.
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_codici_fiscali_display"}
                {* {{{ generazione automatica dei codici fiscali display *}
                <br>
                <table width='100%' align='center'>
                    <tr>
                        <td colspan="4" align='center' class="sottotitolo_testo_bold sfondo_contrasto_generico">
                            Rigenerazione codici fiscali
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td width="25%">
                        </td>
                        <td colspan="2">
                            Verranno generati i codici fiscali mancanti delle tipologie selezionate (verranno lasciati vuoti quelli che per mancanza di dati fondamentali non sono calcolabili). Proseguire?
                        </td>
                        <td width="25%">
                        </td>
                    </tr>
                    <tr>
                        <td>
                        </td>
                        <td class='sottotitolo_testo' align="right">
                            Studenti
                        </td>
                        <td class='sottotitolo_testo'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='operazione' value='studenti'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='generazione_codici_fiscali_update'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                        <td>
                        </td>
                    </tr>
                    <tr>
                        <td>
                        </td>
                        <td class='sottotitolo_testo' align="right">
                            Parenti
                        </td>
                        <td class='sottotitolo_testo'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='operazione' value='parenti'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='generazione_codici_fiscali_update'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                        <td>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_codici_fiscali_update"}
                {* {{{ generazione codici fiscali update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            Generazione codici fiscali ultimata con successo.
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "generazione_barcode_display"}
                {include file="impostazioni/generazione_barcode.tpl"}
            {/if}

            {if $stato_secondario == 'gestione_stampa_badge_display'}
                {include file="impostazioni/gestione_stampa_badge_display.tpl"}
            {/if}

            {if $stato_secondario == 'gestione_stampa_badge_update'}
                {include file="impostazioni/gestione_stampa_badge_update.tpl"}
            {/if}

            {if $stato_secondario == 'gestione_stampa_badge_print'}
                {include file="impostazioni/gestione_stampa_badge_print.tpl"}
            {/if}

            {if $stato_secondario == "parametri_stampa_display"}
                {include file="impostazioni/parametri_stampa.tpl"}
            {/if}

            {if $stato_secondario == "gestione_sms_update"}
                {* {{{ sezione di update ultimato con successo degli sms *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td align='center' class='titolo_testo'>
                            Aggiornamento dati gestione SMS ultimato con successo.
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='gestione_sms_display'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "privilegi_prof_display"}
                {include file="impostazioni/privilegi_prof_display.tpl"}
            {/if}

            {if $stato_secondario == "privilegi_prof_update"}
                {* {{{ gestione privilegi dei professori update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            Definizione dei privilegi generali dei professori ultimata con successo
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "setup_voti_display"}
                {* {{{ Sezione di setup dei voti *}
                <table align="center">
                    <tr>
                        <td align='center' class='sottotitolo_testo'>
                            In questa sezione è possibile definire i parametri e gli schemi dei voti
                        </td>
                    </tr>
                    <tr>
                        <td align='center' class='sottotitolo_testo'>
                            <font color='#AA0000'>{$messaggio}</font>
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='button' value='PARAMETRI' class="btn_pieno sfondo_scuro testo_bianco ombra_testo ripples margin8"
                                    onclick="this.form.stato_secondario.value='setup_voti_display_base'; this.form.submit();">
                                <input type='button' value='SCHEMI' class="btn_pieno sfondo_scuro testo_bianco ombra_testo ripples margin8"
                                    onclick="this.form.stato_secondario.value='setup_voti_display_avanzato'; this.form.submit();">
                                    <input type='button' value='PESI' class="btn_pieno sfondo_scuro testo_bianco ombra_testo ripples margin8"
                                        onclick="this.form.stato_secondario.value='setup_pesi'; this.form.submit();">
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value=''>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "setup_voti_display_base"}
                {include file="impostazioni/setup_voti.tpl"}
            {/if}

            {if $stato_secondario == "setup_voti_display_avanzato"}
                {include file="impostazioni/setup_voti_avanzato.tpl"}
            {/if}

            {if $stato_secondario == "setup_pesi"}
                {include file="impostazioni/setup_pesi.tpl"}
            {/if}

            {if $stato_secondario == "parametri_pagelle_display"}
                {include file="impostazioni/parametri_pagelle.tpl"}
            {/if}

            {if $stato_secondario == "parametri_pagelle_formule"}
                {include file="impostazioni/parametri_pagelle_formule.tpl"}
            {/if}

            {if $stato_secondario == "elenco_scuole"}
                {include file="impostazioni/elenco_scuole.tpl"}
            {/if}

            {if $stato_secondario == "festivita_scuola"}
                {* {{{ gestione del calendario dell'istituto *}
                <table width='100%' align='center'>
                    <tr>
                        <td align='center' class='titolo_funzione'>
                            Sezione di gestione del calendario dell'istituto
                        </td>
                    </tr>
                    {if $messaggio != ""}
                        <tr>
                            <td class='sottotitolo_testo'>
                                {if $errore == "SI"}
                                    <font color='#ff0000'>
                                {/if}
                                {$messaggio}</font>
                            </td>
                        </tr>
                    {/if}
                </table>
                {* {{{ visualizza/Inserisci elenco *}
                <form method='post' action='{$SCRIPT_NAME}' name='form_grid_orario_personale'>
                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                    <input type='hidden' name='stato_secondario' value='festivita_scuola'>
                    <input type='hidden' name='stato_calendario' value='modifica'>
                    <input type='hidden' name='data_corrente' value=''>
                    <input type='hidden' name='current_user' value='{$current_user}'>
                    <input type='hidden' name='current_key' value='{$current_key}'>
                </form>
                <table width='100%' align='center'>
                    <tr>
                        <td colspan='3' align='center' class='sottotitolo_funzione'>
                            Calendario festività istituto
                        </td>
                    </tr>
                    <tr>
                        <td colspan='1' align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                {mastercom_auto_select array_dati=$elenco_settimane name="settimana" onchange="this.form.submit();" value=-1}
                                -1###Selezionare la settimana da copiare@@@
                                {/mastercom_auto_select}
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='festivita_scuola'>
                                <input type='hidden' name='stato_calendario' value='copia_settimane_display'>
                                <input type='hidden' name='periodo_start' value='{$periodo_start}'>
                                <input type='hidden' name='periodo_end' value='{$periodo_end}'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                <table width='100%' align='center'>
                    <tr>
                        <td align='center' class='padding_cella_generica'>
                            {mastercom_calendar_festivita calendar=$elenco_orario type="festivita_scuola" anno_scolastico=$anno_scolastico_utilizzato}
                        </td>
                    </tr>
                </table>
                {* }}} *}

                {if $stato_calendario == "copia_settimane_display"}
                    {* {{{ visualizza/Inserisci elenco *}
                    <br>
                    <form method='post' action='{$SCRIPT_NAME}' name='form_grid_orario_personale'>
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                        <input type='hidden' name='stato_secondario' value='festivita_scuola'>
                        <input type='hidden' name='stato_calendario' value='copia_settimane_update'>
                        <input type='hidden' name='settimana' value='{$settimana}'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center' class='titolo_funzione'>
                                    Sezione replicazione orario della settimana {$settimana_tradotta}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    Spuntare le settimane nelle quali si desidera copiare l'orario della settimana selezionata.
                                </td>
                            </tr>
                        </table>
                        <table width='100%' align='center'>
                            {section name=cont_ext loop=$start}
                                <tr class='sfondo_contrasto_generico'>
                                    {section name=cont loop=$elenco_settimane start=$start[cont_ext] max=4 step=$step}
                                        <td class='padding_cella_generica'>
                                            <input type="checkbox" name="checkbox{$elenco_settimane[cont].valore}" value="1">{$elenco_settimane[cont].nome}<br>
                                        </td>
                                    {/section}
                                </tr>
                            {/section}
                        </table>
                        <table width='100%' align='center'>
                            <tr>
                                <td align='center'>
                                    <input type='image' name='bottone' value='Conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                </td>
                            </tr>
                        </table>
                    </form>
                    {* }}} *}
                {/if}
                <br><br><br>
                <table width="100%" cellpadding="4" cellspacing="0">
                    <tr>
                        <td colspan='3' align='center' class='sottotitolo_funzione'>LEGENDA</td>
                    </tr>
                    <tr valign="top">
                        <td width="40%" style="padding-top: 0.1cm; padding-bottom: 0.1cm; padding-left: 0.1cm; padding-right: 0cm">
                            <p align="right" style="font-weight: bold; color: blue;"><br/>
                                X:
                            </p>
                        </td>
                        <td width="50%" style="padding: 0.1cm">
                            <p align="left"><br/>
                                NON DEFINITO
                            </p>
                        </td>
                    </tr>
                    <tr valign="top">
                        <td width="40%" style="padding-top: 0cm; padding-bottom: 0.1cm; padding-left: 0.1cm; padding-right: 0cm">
                            <p align="right" style="font-weight: bold; color: blue;"><br/>
                                A:
                            </p>
                        </td>
                        <td width="50%" style=" padding-top: 0cm; padding-bottom: 0.1cm; padding-left: 0.1cm; padding-right: 0.1cm">
                            <p align="left"><br/>
                                NORMALE ATTIVITÀ SCOLASTICA
                            </p>
                        </td>
                    </tr>
                    <tr valign="top">
                        <td width="40%" style="padding-top: 0cm; padding-bottom: 0.1cm; padding-left: 0.1cm; padding-right: 0cm">
                            <p align="right" style="font-weight: bold; color: red;"><br/>
                                F:
                            </p>
                        </td>
                        <td width="50%" style="padding-top: 0cm; padding-bottom: 0.1cm; padding-left: 0.1cm; padding-right: 0.1cm">
                            <p align="left"><br/>
                                FESTIVITÀ SCOLASTICA
                            </p>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "accesso_gate_display"}
                {include file="impostazioni/accesso_gate.tpl"}
            {/if}

            {if $stato_secondario == "visualizza_intervalli_predefiniti_orario"}
                {include file="impostazioni/intervalli_predefiniti_orario.tpl"}
            {/if}

            {if $stato_secondario == "gestione_commissioni_display"}
                {include file="impostazioni/gestione_commissioni.tpl"}
            {/if}

            {if $stato_secondario == "export_display"}
                {include file="impostazioni/modulo_esportazioni.tpl"}
            {/if}

            {if $stato_secondario == "spegni_server_display"}
                {* {{{ spegni server display *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td colspan='2' class='titolo_testo'>
                            {mastercom_label}ATTENZIONE!!! Proseguendo verrà spento il server MasterCom e non sarà più possibile fare aggiornamenti o altro, da parte di chiunque, fintanto che questo rimarrà spento, proseguire?{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='spegni_server_update'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ko&label_bg=d11a2e&size={$dimensione_immagini}' alt='Annulla Operazione'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='annulla'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "spegni_server_update"}
                {* {{{ spegni server update *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td class='titolo_testo'>
                            {mastercom_label}Server spento con successo. Chiudere il browser.{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='image' name='bottone' value='conferma' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='default'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_sostituzioni"}
            {* {{{ gestione sostituzioni *}
                <br>
                <table width='50%' align='center'>
                    <tr>
                        <td align="center" class='titolo_testo padding_cella_generica'>
                            {mastercom_label}Sezione spostata in "ORARIO"{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td align='center' class='padding_cella_generica'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                <input type='submit' name='bottone' value='Vai a ORARIO'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='gestione_orario_principale'>
                                <input type='hidden' name='stato_secondario' value='gestione_sostituzioni'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    </tr>
                </table>
                {*{include file="impostazioni/gestione_sostituzioni.tpl"}*}
            {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_certificati_display"}
                {include file="impostazioni/gestione_certificati.tpl"}
            {/if}

            {if $stato_secondario == 'verifica_dati_partenza'}
                {* {{{ sezione di startup del nuovo AS *}
                <table  width='100%'>
                    <tr>
                        <td align='center' class='titolo_funzione sfondo_contrasto_generico'>
                            Controllo dei dati impostati per la partenza:
                        </td>
                        <td align='right' width='20'>
                            <button onclick="window.open('manuali/StartUP_Settembre.pdf', 'manuale');"><img src="icone/manuale_scuro24.gif"></button>
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr>
                        <td align='center' colspan='2' class='sottotitolo_testo'>
                            Controllare i seguenti parametri per una partenza ottimale col sistema MasterCom.<br>
                            Si prega di consultare il manuale relativo qui riportato per i dettagli.
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <table width='100%'>
                                <tr>
                                    <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
                                        Legenda:
                                    </td>
                                </tr>
                                <tr>
                                    <td class='divisore_basso' width='60'>
                                        <img src="icone/icona.php?icon=ok&label_bg=44621C&size=48" />
                                    </td>
                                    <td class='divisore_basso'>
                                        Dati impostati correttamente
                                    </td>
                                </tr>
                                <tr>
                                    <td class='divisore_basso'>
                                        <img src="icone/icona.php?icon=forse&label_bg=FFCC00&size=48" />
                                    </td>
                                    <td class='divisore_basso'>
                                        Dati parzialmente inseriti che necessitano di una verifica.
                                    </td>
                                </tr>
                                <tr>
                                    <td class='padding_cella_generica'>
                                        <img src="icone/icona.php?icon=ko&label_bg=FF0000&size=48" />
                                    </td>
                                    <td class='padding_cella_generica'>
                                        Dati non inseriti.
                                    </td>
                                </tr>
                            </table>
                            <br>
                        </td>
                    </tr>
                    <tr>
                        <td align='center' colspan='2' class='titolo_funzione sfondo_contrasto_generico'>
                            Verifiche effettuate sui dati:
                        </td>
                    </tr>
                    <tr>
                        <td colspan='2'>
                            {foreach from=$mat_controlli key=key item=singolo_controllo}
                                {if $singolo_controllo.stato eq 'OK'}
                                    {assign "color" "44621C"}
                                {elseif $singolo_controllo.stato eq 'FORSE'}
                                    {assign "color" "FFCC00"}
                                {elseif $singolo_controllo.stato eq 'KO'}
                                    {assign "color" "FF0000"}
                                {else}
                                    {assign "color" "44621C"}
                                {/if}
                                <table width='100%'>
                                    <tr>
                                        <td align='center' height='60' width="60">
                                            <img src="icone/icona.php?icon={$singolo_controllo.stato|lower}&label_bg={$color}&size=48" />
                                        </td>
                                        <td class='contenitore_generico sfondo_contrasto_generico'>
                                            <strong>{$singolo_controllo.cosa}:</strong>
                                            <br>
                                            {$singolo_controllo.messaggio}
                                            {if $key == 'studenti_succ' && $singolo_controllo.stato == 'FORSE'}
                                                <div align="center">
                                                    <form method='post' align="center">
                                                        <input type='submit' value='Abbinamento degli studenti a classi successive'>
                                                        <input type='hidden' name='form_stato' value='amministratore'>
                                                        <input type='hidden' name='stato_principale' value='ricerca_full'>
                                                        <input type='hidden' name='stato_secondario' value='ricerca_display'>
                                                        <input type='hidden' name='form_azione_ricerca' value='ABBINAMENTI'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                </div>
                                            {/if}
                                        </td>
                                    </tr>
                                </table>
                                <br>
                            {/foreach}
                        </td>
                    </tr>
                </table>
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_classi_indirizzi_display" or $stato_secondario == "gestione_classi_indirizzi_anno_succ_display"}
                {* {{{ gestione classi e indirizzi *}
                {include file="impostazioni/setup_indirizzi_classi.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_materie_curricolo_display" or $stato_secondario == "gestione_materie_curricolo_anno_succ_display"}
                {* {{{ gestione curriculum degli studi *}
                {include file="impostazioni/setup_materie_curricolo.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_materie_display"}
                {* {{{ gestione materie *}
                {if $anno_inizio <= '2015'}
                    {include file="impostazioni/setup_materie.tpl"}
                {else}
                    {include file="impostazioni/setup_materie_new.tpl"}
                {/if}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_codici_ministeriali_display"}
                {* {{{ gestione materie *}
                {include file="impostazioni/setup_codici_ministeriali.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_studenti_piani_studio_medie"}
                {* {{{ gestione materie *}
                {include file="impostazioni/setup_piani_studio.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_quadri_orario_display"}
                {* {{{ gestione materie *}
                {include file="impostazioni/setup_quadri_orario.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_abbin_materie_quadri_orario_display"}
                {include file="impostazioni/setup_abbin_materie_quadri_orario.tpl"}
            {/if}

            {if $stato_secondario == "gestione_abbin_mastercom_trentino_display"}
                {include file="impostazioni/setup_abbin_mastercom_trentino.tpl"}
            {/if}

            {if $stato_secondario == "gestione_simboli_annotazioni_display"}
                {* {{{ gestione annotazioni *}
                {include file="impostazioni/setup_simboli_annotazioni.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_utenti_display"}
                {* {{{ gestione utenti nuova *}
                {include file="impostazioni/setup_utenti.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "permessi_microsoft_amministratore"}
                {* {{{ Autorizzazzione microsoft da parte dell'amministratore per gli utenti dell'organizzazione *}
                {include file="impostazioni/permessi_microsoft_amministratore.tpl"}
                {* }}} *}
            {/if}

            {if $stato_secondario == "gestione_collegamenti_multipli_amministratori"}
                {include file="impostazioni/gestione_collegamenti_multipli_amministratori.tpl"}
            {/if}

            {if $stato_secondario == "generazione_chiavi_utenti"}
                {* {{{ generazione delle chiavi di accesso per registro elettronico dei professori *}
                <br>
                <form method='post' action='{$SCRIPT_NAME}' name='form_chiavi_prof'>
                    <table width='100%' align='center'>
                        <tr>
                            <td colspan="4" align='center' class="sottotitolo_testo_bold sfondo_contrasto_generico">
                                Rigenerazione chiavi registro elettronico docenti
                            </td>
                        </tr>
                    </table>
                    <br>
                    <table width='50%' align='center'>
                        <tr>
                            <td colspan='4'>
                                Rigenerare il codice di accesso delle chiavi USB per il registro elettronico degli utenti selezionati?<br>ATTENZIONE!!!<br>
                                Procedendo le vecchie penne USB non saranno pi&ugrave; valide fino a che non vi venga copiato il nuovo codice di accesso.
                            </td>
                        </tr>
                       <tr><td><br></td></tr>
                        {section name=cont loop=$professori}
                            <tr class='{cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                                <td size='5%'>
                                    <input checked type='checkbox' name='mat_utenti[]' value='{$professori[cont].id_utente}'>
                                </td>
                                <td>
                                    {$professori[cont].cognome}	{$professori[cont].nome}
                                </td>
                                <td>
                                    {if $professori[cont].utente_registro != ''}
                                        ************************
                                    {/if}
                                </td>
                                <td size='5%' align='right'>
                                    <img name='bottone' value='Scarica chiave' src='icone/icona.php?icon=pendrive&label_bg=' alt='Scarica chiave'
                                         onclick = "document.forms['form_chiavi_prof'].id_utente.value ={$professori[cont].id_utente};
                                                 document.forms['form_chiavi_prof'].operazione.value = 'scarica_chiave_utente';
                                                 document.forms['form_chiavi_prof'].submit();">
                                </td>
                            </tr>
                    </form>
                {/section}
    </table>
    <table width='100%' align='center'>
        <tr>
            <td align='center' colspan='4'>
                <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma'>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='generazione_chiavi_utenti'>
                <input type='hidden' name='operazione' value=''>
                <input type='hidden' name='id_utente' value=''>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </td>
            </form>
        </tr>
    </table>
    {* }}} *}
{/if}

{if $stato_secondario == "export_siis"}
    {* {{{ esportazioni SIIS *}
    {include file="impostazioni/export_siis.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "flussi_siis"}
    {* {{{ servizi e richieste SIIS *}
    {include file="impostazioni/flussi_siis.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "flussi_sial"}
    {* {{{ servizi e richieste SIIS *}
    {include file="impostazioni/flussi_sial.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "controllo_ip"}
    {* {{{ servizi e richieste SIIS *}
    {include file="include_controllo_ip.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "importa_preiscrizioni_display"}
    {* {{{ esportazioni SIIS *}
    {include file="impostazioni/import_preiscrizioni.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "importa_dati_esami_display"}
    {* {{{ esportazioni SIIS *}
    {include file="impostazioni/import_commissione_web.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "gestione_adozione_libri_display"}
    {* {{{ gestione annotazioni *}
    {include file="impostazioni/include_setup_adozione_libri.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "gestione_campi_liberi_voti_display"}
    {* {{{ gestione annotazioni *}
    {if $old_version}
    {include file="impostazioni/include_setup_campi_liberi_voti.tpl"}
    {else}
    {include file="impostazioni/include_setup_campi_liberi_voti_new.tpl"}
    {/if}
    {* }}} *}
{/if}

{if $stato_secondario == "gestione_import_doc_display"}
    {* {{{ importazione documenti studenti *}
    {include file="impostazioni/include_import_doc.tpl"}
    {* }}} *}
{/if}

{if $stato_secondario == "storico_portale_genitori"}
    {*//{{{ Sezione di gestione del servizio mensa per i docenti *}
    <tr>
        <td colspan='9' class='titolo_funzione sfondo_contrasto_generico'>
            Impostazioni portale genitori
        </td>
    </tr>
    {if $superutente_int == 'SI'}
        <tr>
            <td colspan='9' align='center' class='padding_cella_generica'>
                <i>All'interno di Setup-A04 è possibile personalizzare i parametri dell'anno corrente per ogni indirizzo o classe</i>
            </td>
        </tr>
    {/if}
    <tr>
        <td colspan='9' align="center">
            <form method='post' action='{$SCRIPT_NAME}'>
                <div style="padding:0 0 15px 0; font-weight:bold">
                    {counter assign=colspan start=2 print=false}
                    {foreach from=$elenco_anni_storici item=anno_storico}
                        {counter}
                    {/foreach}

                    {assign var=mastercom_db value=$anni_per_parametri[0]}
                </div>
                <table style="border-collapse: separate; width:100%">
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico">
                        <td class="padding_cella_generica" colspan="{$colspan}">Parametri</td>
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico">
                        <td class="padding_cella_generica"></td>
                        <td class="padding_cella_generica" colspan="{$colspan-1}">Portali storici</td>
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico">
                        <td class="padding_cella_generica"></td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                            <td class="padding_cella_generica">Abilita
                                <input type="checkbox" name="abilita_parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VISUALIZZAZIONE_STORICA_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VISUALIZZAZIONE_STORICA_PORTALE.valore == 'SI'} checked {/if} onchange="javascript:setCampiStorici('{$anno_storico|replace:"mastercom_":""}');">
                            </td>
                        {/foreach}
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico">
                        <td>Nome</td>
                        {foreach from=$elenco_anni_storici item=anno_storico}
                            <td>A.S. {$anno_storico}/{$anno_storico+1}</td>
                        {/foreach}
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">GENERALI</td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_AGENDA_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_AGENDA_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_AGENDA_PORTALE.valore == 'SI'} checked {/if} onload="javascript:setCampiStorici('{$anno_storico|replace:"mastercom_":""}');">
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_ALTERNANZA_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_ALTERNANZA_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_ALTERNANZA_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_ARGOMENTI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_ARGOMENTI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_ARGOMENTI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_COMPITI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_COMPITI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_COMPITI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    {*
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_COMUNICAZIONI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_COMUNICAZIONI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_COMUNICAZIONI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>*}
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_DOCUMENTI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_PORTALE.nome}]">
                                <option value="SI" {if $elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_PORTALE.valore == 'SI'} selected{/if}>
                                    SI
                                </option>
                                <option value="NO" {if $elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_PORTALE.valore == 'NO'} selected{/if}>
                                    NO
                                </option>
                                <option value="SI_SOLO_CON_VALORI" {if $elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_PORTALE.valore == 'SI_SOLO_CON_VALORI'} selected{/if}>
                                    SI SOLO CON VALORI
                                </option>
                            </select>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_UTILITA_DOCENTI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_UTILITA_DOCENTI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_UTILITA_DOCENTI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_ESTRATTO_CONTO_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_ESTRATTO_CONTO_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_ESTRATTO_CONTO_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MATERIALE_DIDATTICO_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MATERIALE_DIDATTICO_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MATERIALE_DIDATTICO_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MENSE_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$mastercom_db.ABILITA_MENSE_PORTALE.nome}]">
                                <option value="0" {if $elenco_parametri.$anno_storico.ABILITA_MENSE_PORTALE.valore == 0} selected{/if}>
                                    Non abilitato
                                </option>
                                <option value="1" {if $elenco_parametri.$anno_storico.ABILITA_MENSE_PORTALE.valore == 1} selected{/if}>
                                    Visione pasti fruiti, non può prenotare
                                </option>
                                <option value="2" {if $elenco_parametri.$anno_storico.ABILITA_MENSE_PORTALE.valore == 2} selected{/if}>
                                    Può prenotare
                                </option>
                            </select>
                        {/if}
                        </td>
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MESSAGGI_PORTALE.descrizione}
                            <span class="tooltip">&#9432;
                                <span class="tooltiptext">
                                    Impostando a "NO" questo parametro si disattiva contestualmente la Bacheca
                                </span>
                            </span>
                        </td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            {*<input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 'SI'} checked {/if}>*}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.nome}]">
                                <option value="0" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 0 || $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 'NO'} selected{/if}>
                                    NO
                                </option>
                                <option value="1" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 1 || $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 'SI'} selected{/if}>
                                    Lettura mess. e Conferma
                                </option>
                                <option value="2" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 2} selected{/if}>
                                    Risposta ai messaggi
                                </option>
                                <option value="3" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_PORTALE.valore == 3} selected{/if}>
                                    Scrittura messaggi
                                </option>
                            </select>
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">Messaggi in bacheca per studenti e genitori dal Ministero
                            <span class="tooltip">&#9432;
                                <span class="tooltiptext">
                                    Si fa riferimento ai messaggi automatici generati e inviati direttamente dal Ministero dell'Istruzione e del Merito, attraverso MasterCom, con l’obiettivo di comunicare in modo tempestivo ed efficiente con i genitori e gli studenti
                                </span>
                            </span>
                        </td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MESSAGGI_BACHECA_MINISTERO.nome}]" onchange="confermaCambioParametroMinistero(this);">
                                <option value="SI" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_BACHECA_MINISTERO.valore == 'SI'} selected{/if}>
                                    SI
                                </option>
                                <option value="NO" {if $elenco_parametri.$anno_storico.ABILITA_MESSAGGI_BACHECA_MINISTERO.valore == 'NO'} selected{/if}>
                                    NO
                                </option>
                            </select>
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_ANNOTAZIONI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_ANNOTAZIONI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_ANNOTAZIONI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_UTILITA_ORARIO_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_UTILITA_ORARIO_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_UTILITA_ORARIO_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_SERVIZI_GIORNALIERI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_SERVIZI_GIORNALIERI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_SERVIZI_GIORNALIERI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    {if $opt_tutor_docente == 1 || $opt_tutor_pannello == 1}
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.RESOCONTI_TUTOR.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('RESOCONTI_TUTOR', $elenco_parametri.$anno_storico)}
                            {mastercom_auto_select
                                name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.RESOCONTI_TUTOR.nome`]"
                                id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.RESOCONTI_TUTOR.nome`]"
                                value=$elenco_parametri.$anno_storico.RESOCONTI_TUTOR.valore}
                                ###@@@
                                NO###NO@@@
                                SI###SI
                            {/mastercom_auto_select}
                        {/if}
                        </td>
                        {/foreach}
                    </tr>
                    {/if}
                    {if $superutente_int == "SI"}
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_DOCUMENTI_AMMINISTRATIVI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_AMMINISTRATIVI_PORTALE.nome, $elenco_parametri.$anno_storico)}
                                <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_AMMINISTRATIVI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_DOCUMENTI_AMMINISTRATIVI_PORTALE.valore == 'SI'} checked {/if} onload="javascript:setCampiStorici('{$anno_storico|replace:"mastercom_":""}');">
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    {/if}
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">PAGELLE</td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_PAGELLE_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_PAGELLE_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_PAGELLE_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_CREDITI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MOSTRA_CREDITI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MOSTRA_CREDITI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_ESITO_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MOSTRA_ESITO_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MOSTRA_ESITO_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_VOTO_AMMISSIONE_MEDIE_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('ABILITA_MOSTRA_VOTO_AMMISSIONE_MEDIE_PORTALE', $elenco_parametri.$anno_storico)}
                            {mastercom_auto_select
                                name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MOSTRA_VOTO_AMMISSIONE_MEDIE_PORTALE.nome`]"
                                id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MOSTRA_VOTO_AMMISSIONE_MEDIE_PORTALE.nome`]"
                                value=$elenco_parametri.$anno_storico.ABILITA_MOSTRA_VOTO_AMMISSIONE_MEDIE_PORTALE.valore}
                                NO###NO@@@
                                SI###SI
                            {/mastercom_auto_select}
                        {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_VOTO_ESAME_MEDIE_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MOSTRA_VOTO_ESAME_MEDIE_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MOSTRA_VOTO_ESAME_MEDIE_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_VOTI_SUFF_STUDENTI_SOSPESI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_SUFF_STUDENTI_SOSPESI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_SUFF_STUDENTI_SOSPESI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_VOTI_INSUFF_STUDENTI_SOSPESI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_INSUFF_STUDENTI_SOSPESI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_INSUFF_STUDENTI_SOSPESI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_VOTI_SOLO_INSUFF_STUDENTI_SOSPESI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_SOLO_INSUFF_STUDENTI_SOSPESI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_SOLO_INSUFF_STUDENTI_SOSPESI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_MEDIA_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MOSTRA_MEDIA_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MOSTRA_MEDIA_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_VOTI_SUFF_INSUFF_STUDENTI_BOCCIATI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_SUFF_INSUFF_STUDENTI_BOCCIATI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_SUFF_INSUFF_STUDENTI_BOCCIATI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="button"
                                   name="bottone[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.nome}]"
                                   value="GIUDIZI SOSPESI"
                                   onclick="javascript:openPopup('sospesi','{$anno_storico|replace:"mastercom_":""}', '{$elenco_parametri.$anno_storico.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.nome}');">
                            <input type="hidden"
                                   name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.nome}]"
                                   value="{$elenco_parametri.$anno_storico.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.valore}">
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MESSAGGIO_BOCCIATI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="button"
                                   name="bottone[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_BOCCIATI_PORTALE.nome}]"
                                   value="BOCCIATI"
                                   onclick="javascript:openPopup('bocciati','{$anno_storico|replace:"mastercom_":""}', '{$elenco_parametri.$anno_storico.MESSAGGIO_BOCCIATI_PORTALE.nome}');">
                            <input type="hidden"
                                   name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_BOCCIATI_PORTALE.nome}]"
                                   value="{$elenco_parametri.$anno_storico.MESSAGGIO_BOCCIATI_PORTALE.valore}">
                        </td>
                        {/foreach}
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">VOTI</td>
                    </tr>
                    {*<tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">
                            <div id="vc" class="padding-bottom-4">Voti e competenze</div>
                            <div id="opv" class="smaller padding-left-8 vertical-center sfondo_contrasto_generico_leggero min-height-28">- Orario pubblicazione dei voti e delle competenze</div>
                            <div id="rpv" class="smaller padding-left-8 vertical-center min-height-28">- Ritardo della pubblicazione dei voti e competenze (in minuti; valido anche per quaderno elettronico)</div>
                            <div id="smv" class="smaller padding-left-8 vertical-center sfondo_contrasto_generico_leggero min-height-28">- Sezione media voti</div>
                        </td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <div class="padding-bottom-4 vertical-center horizontal-center vc">
                                <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_PORTALE.valore == 'SI'} checked {/if}>
                            </div>
                            <div class="smaller vertical-center horizontal-center sfondo_contrasto_generico_leggero min-height-28 opv">
                                {if array_key_exists('ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE', $elenco_parametri.$anno_storico)}
                                    {mastercom_smart_hours
                                     name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome`]"
                                     h_min="1" h_max="24" no_minutes="t" allow_null_value="Y" selected="`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore`"
                                     onchange="updateOrarioPubblicazioneVotiPortale(this.value, 'parametri[`$anno_storico|replace:'mastercom_':''`][`$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome`]');"}
                                {/if}
                            </div>
                            <div class="smaller vertical-center horizontal-center min-height-28 rpv">
                                {if array_key_exists('RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE', $elenco_parametri.$anno_storico)}
                                    <input type="number"
                                        id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome}]"
                                        name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome}]"
                                        value="{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore}"
                                        min="0"
                                        style="width: 50px;"
                                        {if $elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore!=''}readonly{/if}
                                    >
                                {/if}
                            </div>
                            <div class="smaller vertical-center horizontal-center sfondo_contrasto_generico_leggero min-height-28 smv">
                                {if array_key_exists('ABILITA_MEDIA_SEZIONE_VOTI_PORTALE', $elenco_parametri.$anno_storico)}
                                    {mastercom_auto_select
                                        name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.nome`]"
                                        id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.nome`]"
                                        value=$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.valore}
                                        NO###NO@@@
                                        SI###SI
                                    {/mastercom_auto_select}
                                {/if}
                            </div>
                        </td>
                        {/foreach}
                    </tr>*}
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">Voti e competenze</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_VOTI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_VOTI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">Orario pubblicazione dei voti e delle competenze</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists('ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE', $elenco_parametri.$anno_storico)}
                                {mastercom_smart_hours
                                 name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome`]"
                                 h_min="1" h_max="24" no_minutes="t" allow_null_value="Y" selected="`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore`"
                                 onchange="updateOrarioPubblicazioneVotiPortale(this.value, 'parametri[`$anno_storico|replace:'mastercom_':''`][`$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome`]');"}
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">Ritardo della pubblicazione dei voti e competenze (in minuti; valido anche per quaderno elettronico</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE', $elenco_parametri.$anno_storico)}
                            <input type="number"
                                id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome}]"
                                name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.nome}]"
                                value="{$elenco_parametri.$anno_storico.RITARDO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore}"
                                min="0"
                                style="width: 50px;"
                                {if $elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_VOTI_COMPETENZE_PORTALE.valore!=''}readonly{/if}
                            >
                        {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">Sezione media voti</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('ABILITA_MEDIA_SEZIONE_VOTI_PORTALE', $elenco_parametri.$anno_storico)}
                            {mastercom_auto_select
                                name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.nome`]"
                                id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.nome`]"
                                value=$elenco_parametri.$anno_storico.ABILITA_MEDIA_SEZIONE_VOTI_PORTALE.valore}
                                NO###NO@@@
                                SI###SI
                            {/mastercom_auto_select}
                        {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_PRESA_VISIONE_VOTI.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('ABILITA_PRESA_VISIONE_VOTI', $elenco_parametri.$anno_storico)}
                            {mastercom_auto_select
                                name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_VOTI.nome`]"
                                id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_VOTI.nome`]"
                                value=$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_VOTI.valore}
                                NO###NO@@@
                                SI###SI
                            {/mastercom_auto_select}
                        {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">ASSENZE</td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_ASSENZE_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_ASSENZE_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_ASSENZE_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_MOSTRA_STATO_ASSENZA_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_MOSTRA_STATO_ASSENZA_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_MOSTRA_STATO_ASSENZA_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_GIUSTIFICAZIONI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_GIUSTIFICAZIONI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_GIUSTIFICAZIONI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">NOTE DISCIPLINARI</td>
                    </tr>
                    {*<tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">
                            {$elenco_parametri.$mastercom_db.ABILITA_NOTE_DISCIPLINARI_PORTALE.descrizione}
                            <br>
                            <small>
                            - {$elenco_parametri.$mastercom_db.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.descrizione}
                            </small>
                        </td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_NOTE_DISCIPLINARI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_NOTE_DISCIPLINARI_PORTALE.valore == 'SI'} checked {/if}>
                            {if array_key_exists('ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE', $elenco_parametri.$anno_storico)}
                                <br>
                                {mastercom_smart_hours
                                 name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.nome`]"
                                 h_min="1" h_max="24" no_minutes="t" allow_null_value="Y" selected="`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.valore`"}
                            {/if}
                        </td>
                        {/foreach}
                    </tr>*}

                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_NOTE_DISCIPLINARI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_NOTE_DISCIPLINARI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_NOTE_DISCIPLINARI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists('ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE', $elenco_parametri.$anno_storico)}
                                {mastercom_smart_hours
                                 name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.nome`]"
                                 h_min="1" h_max="24" no_minutes="t" allow_null_value="Y" selected="`$elenco_parametri.$anno_storico.ORARIO_PUBBLICAZIONE_NOTE_DISCIPLINARI_PORTALE.valore`"}
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_PRESA_VISIONE_NOTE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                        {if array_key_exists('ABILITA_PRESA_VISIONE_NOTE', $elenco_parametri.$anno_storico)}
                            {mastercom_auto_select
                                name="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_NOTE.nome`]"
                                id="parametri[`$anno_storico|replace:"mastercom_":""`][`$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_NOTE.nome`]"
                                value=$elenco_parametri.$anno_storico.ABILITA_PRESA_VISIONE_NOTE.valore}
                                NO###NO@@@
                                SI###SI
                            {/mastercom_auto_select}
                        {/if}
                        </td>
                        {/foreach}
                    </tr>

                    <tr align="center" class="sottotitolo_funzione sfondo_scuro_generico collapse_header" style="cursor:pointer;">
                        <td class="padding_cella_generica" colspan="{$colspan}">COLLOQUI</td>
                    </tr>
                    <tr align="center" class="sottotitolo_funzione sfondo_azzurro"  style="background-color: #0090D5; font-weight: normal;">
                        <td class="padding_cella_generica" style="color: white;" colspan="{$colspan}">Colloqui generali</td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_COLLOQUI_PORTALE_GENERALI.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.ABILITA_COLLOQUI_PORTALE_GENERALI.nome, $elenco_parametri.$anno_storico)}
                                <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_COLLOQUI_PORTALE_GENERALI.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_COLLOQUI_PORTALE_GENERALI.valore == 'SI'} checked {/if}>
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO_GENERALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO_GENERALE.nome, $elenco_parametri.$anno_storico)}
                                <input type="number"
                                    id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO_GENERALE.nome}]"
                                    name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO_GENERALE.nome}]"
                                    value="{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO_GENERALE.valore}"
                                    min="0"
                                    style="width: 50px;"
                                >
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.descrizione}
                                    <span class="tooltip">&#9432;
                                        <span class="tooltiptext">
                                            Numerica: il genitore prenota il posto (senza orario)<br>
											Orari: il genitore prenota l'orario preciso del colloquio<br>
											Disponibilità: il genitore prenota il colloquio senza indicazione di posto o orario</span>
                                    </span></td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.nome, $elenco_parametri.$anno_storico)}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$mastercom_db.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.nome}]">
                                <option value="NUMERICA" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.valore == 'NUMERICA'} selected{/if}>
                                    Numerica
                                </option>
                                <option value="ORARI" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.valore == 'ORARI'} selected{/if}>
                                    Orari
                                </option>
                                <option value="DISPONIBILITA" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI_GENERALE.valore == 'DISPONIBILITA'} selected{/if}>
                                    Disponibilità
                                </option>
                            </select>
                            {/if}
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_contrasto_generico">
{*                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.descrizione}</td>*}
                        <td class="padding_cella_generica">Numero massimo di colloqui per singolo docente che una famiglia può prenotare (se 0 non vi sono limiti):</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.nome, $elenco_parametri.$anno_storico)}
                                <input type="number"
                                    id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.nome}]"
                                    name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.nome}]"
                                    value="{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.valore}"
                                    min="0"
                                    style="width: 50px;"
                                >
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
{*                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.descrizione}</td>*}
                        <td class="padding_cella_generica">Periodo di applicazione del numero massimo
                                    <span class="tooltip">&#9432;
                                        <span class="tooltiptext">
                                            A utilizzo: il limite impostato rimane valido dopo la fruizione del colloquio (es. limite 3; il genitore prenota 1 colloquio, gliene resteranno 2; dopo tale colloquio avrà di nuovo limite 3)<br>
                                            Periodo: applicato ai quadrimestri/trimestri<br>
                                            Anno scolastico: applicato a tutto l'anno</span>
                                    </span></td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.nome, $elenco_parametri.$anno_storico)}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$mastercom_db.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.nome}]">
                                <option value="MAX_PRENOTAZIONI_ATTIVE" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.valore == 'MAX_PRENOTAZIONI_ATTIVE'} selected{/if}>
                                    A utilizzo
                                </option>
                                <option value="MAX_COLLOQUI_PERIODO" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.valore == 'MAX_COLLOQUI_PERIODO'} selected{/if}>
                                    Periodo
                                </option>
                                <option value="MAX_COLLOQUI_ANNO" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO_GENERALE.valore == 'MAX_COLLOQUI_ANNO'} selected{/if}>
                                    Anno scolastico
                                </option>
                            </select>
                            {/if}
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>

                    <tr align="center" class="sottotitolo_funzione sfondo_azzurro" style="background-color: #0090D5; font-weight: normal;">
                        <td class="padding_cella_generica" style="color: white;" colspan="{$colspan}">Colloqui individuali</td>
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.ABILITA_COLLOQUI_PORTALE.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            <input type="checkbox" name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.ABILITA_COLLOQUI_PORTALE.nome}]" {if $elenco_parametri.$anno_storico.ABILITA_COLLOQUI_PORTALE.valore == 'SI'} checked {/if}>
                        </td>
                        {/foreach}
                    </tr>


                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO.nome, $elenco_parametri.$anno_storico)}
                                <input type="number"
                                    id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO.nome}]"
                                    name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO.nome}]"
                                    value="{$elenco_parametri.$anno_storico.LIMITE_ORARIO_PRENOTAZIONE_COLLOQUIO.valore}"
                                    min="0"
                                    style="width: 50px;"
                                >
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MODALITA_PRENOTAZIONE_COLLOQUI.descrizione}
                                    <span class="tooltip">&#9432;
                                        <span class="tooltiptext">
                                            Numerica: il genitore prenota il posto (senza orario)<br>
											Orari: il genitore prenota l'orario preciso del colloquio<br>
											Disponibilità: il genitore prenota il colloquio senza indicazione di posto o orario</span>
                                    </span></td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI.nome, $elenco_parametri.$anno_storico)}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$mastercom_db.MODALITA_PRENOTAZIONE_COLLOQUI.nome}]">
                                <option value="NUMERICA" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI.valore == 'NUMERICA'} selected{/if}>
                                    Numerica
                                </option>
                                <option value="ORARI" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI.valore == 'ORARI'} selected{/if}>
                                    Orari
                                </option>
                                <option value="DISPONIBILITA" {if $elenco_parametri.$anno_storico.MODALITA_PRENOTAZIONE_COLLOQUI.valore == 'DISPONIBILITA'} selected{/if}>
                                    Disponibilità
                                </option>
                            </select>
                            {/if}
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_contrasto_generico">
{*                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.MAX_POSTI_PRENOTABILI_COLLOQUIO.descrizione}</td>*}
                        <td class="padding_cella_generica">Numero massimo di colloqui per singolo docente che una famiglia può prenotare (se 0 non vi sono limiti):</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO.nome, $elenco_parametri.$anno_storico)}
                                <input type="number"
                                    id="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO.nome}]"
                                    name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO.nome}]"
                                    value="{$elenco_parametri.$anno_storico.MAX_POSTI_PRENOTABILI_COLLOQUIO.valore}"
                                    min="0"
                                    style="width: 50px;"
                                >
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    <tr class="sfondo_base_generico">
{*                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.descrizione}</td>*}
                        <td class="padding_cella_generica">Periodo di applicazione del numero massimo
                                    <span class="tooltip">&#9432;
                                        <span class="tooltiptext">
                                            A utilizzo: il limite impostato rimane valido dopo la fruizione del colloquio (es. limite 3; il genitore prenota 1 colloquio, gliene resteranno 2; dopo tale colloquio avrà di nuovo limite 3)<br>
                                            Periodo: applicato ai quadrimestri/trimestri<br>
                                            Anno scolastico: applicato a tutto l'anno</span>
                                    </span></td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        {if $anno_storico|replace:"mastercom_":"" == $anno_now|replace:"/":"_"}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.nome, $elenco_parametri.$anno_storico)}
                            <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$mastercom_db.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.nome}]">
                                <option value="MAX_PRENOTAZIONI_ATTIVE" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.valore == 'MAX_PRENOTAZIONI_ATTIVE'} selected{/if}>
                                    A utilizzo
                                </option>
                                <option value="MAX_COLLOQUI_PERIODO" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.valore == 'MAX_COLLOQUI_PERIODO'} selected{/if}>
                                    Periodo
                                </option>
                                <option value="MAX_COLLOQUI_ANNO" {if $elenco_parametri.$anno_storico.TIPO_MAX_POSTI_PRENOTABILI_COLLOQUIO.valore == 'MAX_COLLOQUI_ANNO'} selected{/if}>
                                    Anno scolastico
                                </option>
                            </select>
                            {/if}
                        </td>
                        {/if}
                        {/foreach}
                        <td class="padding_cella_generica" align="center"></td>
                    </tr>
                    <tr class="sfondo_contrasto_generico">
                        <td class="padding_cella_generica">{$elenco_parametri.$mastercom_db.RICHIESTA_COLLOQUI_INDIVIDUALI.descrizione}</td>
                        {foreach from=$anni_per_parametri item=anno_storico}
                        <td class="padding_cella_generica" align="center">
                            {if array_key_exists($elenco_parametri.$anno_storico.RICHIESTA_COLLOQUI_INDIVIDUALI.nome, $elenco_parametri.$anno_storico)}
                                <select name="parametri[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.RICHIESTA_COLLOQUI_INDIVIDUALI.nome}]">
                                    <option value="SI" {if $elenco_parametri.$anno_storico.RICHIESTA_COLLOQUI_INDIVIDUALI.valore == 'SI'} selected{/if}>
                                        SI
                                    </option>
                                    <option value="NO" {if $elenco_parametri.$anno_storico.RICHIESTA_COLLOQUI_INDIVIDUALI.valore == 'NO'} selected{/if}>
                                        NO
                                    </option>
                                </select>
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                </table>
                <br>
                <input type='submit' value='{mastercom_label}Salva{/mastercom_label}'>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='storico_portale_genitori'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='operazione' value='salva_parametri_portale'>
                <input type='hidden' name='current_key' value='{$current_key}'>

                <script type="text/javascript" language="javascript">
                    document.addEventListener("DOMContentLoaded", function () {
                        const tooltips = document.querySelectorAll('.tooltiptext');
                        tooltips.forEach(tooltip => {
                            tooltip.style.transform = 'translateX(200px)';
                            tooltip.style.padding = '6px';
                        });
                    });
                </script>
            </form>
        </td>
    </tr>
    {*//}}} *}
{/if}

{if $stato_secondario == "gestione_anagrafica_risorse_display"}
	{* {{{ gestione materie *}
	{include file="impostazioni/setup_anagrafica_risorse.tpl"}
	{* }}} *}
{/if}

{if $stato_secondario == "gestione_corsi_display"}
    {*{if $versione_registro_docente == 'VERSIONE_2'}
        {include file="impostazioni/setup_corsi_v2.tpl"}
    {else}*}
        {include file="impostazioni/setup_corsi.tpl"}
    {*{/if}*}
{/if}

{if $stato_secondario == "gestione_competenze"}
	{* {{{ gestione competenze *}
	{include file="impostazioni/setup_competenze.tpl"}
	{* }}} *}
{/if}

{if $stato_secondario == "scelta_valutazioni_competenze_massiva"}
	{* {{{ assegnaizone massiva schemi valutazioni a competenze *}
	{include file="impostazioni/setup_scelta_valutazioni_competenze_massiva.tpl"}
	{* }}} *}
{/if}

{if $stato_secondario == "manutenzione_competenze"}
	{* {{{ manutenzione delle competenze per nexus *}
	{include file="impostazioni/setup_manutenzione_competenze.tpl"}
	{* }}} *}
{/if}

{if $stato_secondario == "gestione_operazioni_preliminari_display"}
    {include file="impostazioni/setup_gestione_operazioni_preliminari.tpl"}
{/if}

{if $stato_secondario == "gestione_indicatori"}
    {include file="impostazioni/gestione_indicatori.tpl"}
{/if}

{if $stato_secondario == "elenco_motivazioni_crediti"}
    {include file="impostazioni/elenco_motivazioni_crediti.tpl"}
{/if}

{if $stato_secondario == "gestione_elezioni"}
    {include file="impostazioni/gestione_elezioni.tpl"}
{/if}

{if $stato_secondario == "gestione_tag"}
    {include file="impostazioni/gestione_tag.tpl"}
{/if}

{if $stato_secondario == "gestione_template_word"}
    {include file="impostazioni/gestione_template_word.tpl"}
{/if}

{if $stato_secondario == "gestione_account_mail" or $stato_secondario == "gestione_account_mail_mc2"}
    {include file="impostazioni/gestione_account_mail.tpl"}
{/if}

{if $stato_secondario == "gestione_setup_mensa"}
    {include file="impostazioni/gestione_setup_mensa.tpl"}
{/if}

{if $stato_secondario == "mensa_gestione_pasti_quotidiana"}
    {include file="impostazioni/mensa_gestione_pasti_quotidiana.tpl"}
{/if}

{if $stato_secondario == "gestione_setup_optionals"}
    {include file="impostazioni/gestione_setup_optionals.tpl"}
{/if}

{if $stato_secondario == "sezioni_quaderno"}
    {include file="impostazioni/sezioni_quaderno.tpl"}
{/if}

{if $stato_secondario == "permessi_messenger"}
    {include file="impostazioni/permessi_messenger.tpl"}
{/if}

{if $stato_secondario == "visualizza_permessi_messenger"}
    {include file="impostazioni/visualizza_permessi_messenger.tpl"}
{/if}

{if $stato_secondario == "rigenerazione_comunicazione_password"}
    <script type="text/javascript" language="javascript" src="javascript/display_index_amministratore_studenti.js"></script>
    <form method='post' action='{$SCRIPT_NAME}'>
        <h2>{mastercom_label}Generazione e Comunicazione nome utente e password selettiva{/mastercom_label}</h2>

        <div align="center">
            {if $tipo_utente == 'parente'}
                <button type="button"
                    class="btn_pieno sfondo_azzurro testo_bianco ombra_testo ripples"
                    onclick="$('#tipo_utente').val('studente'); this.form.submit();"
                    >{mastercom_label}Passa a studenti{/mastercom_label}</button>
            {elseif $tipo_utente == 'studente'}
                <button type="button"
                    class="btn_pieno sfondo_azzurro testo_bianco ombra_testo ripples"
                    onclick="$('#tipo_utente').val('parente'); this.form.submit();"
                    >{mastercom_label}Passa a genitori{/mastercom_label}</button>
            {/if}
        </div>

        {if $tipo_utente == 'parente'}
            <p>{mastercom_label}Di quale classe visualizzare i genitori per la selezione?{/mastercom_label}</p>
        {elseif $tipo_utente == 'studente'}
            <p>{mastercom_label}Di quale classe visualizzare gli studenti per la selezione?{/mastercom_label}</p>
        {/if}
        <table width="100%">
            <tr id='row_head_classes' class='sfondo_setup_utenti'>
                {* //{{{  scelta classi *}
                <td colspan='2' width='100%'>
                {if $id_classe gt 0}
                    <img  id='icon_classes' name='bottone' value='Conferma' src='icone/icona.php?icon=entra' alt='Apri scelta classi' onclick="mostra_riga('row_grid_classes', this);
                    mostra_riga('row_grid_operations', this);">
                {else}
                    <img  id='icon_classes' name='bottone' value='Conferma' src='icone/icona.php?icon=indietro' alt='Apri scelta classi' onclick="mostra_riga('row_grid_classes', this);
                    mostra_riga('row_grid_operations', this);">
                {/if}
                <b>{mastercom_label}Selezione classi{/mastercom_label}</b>
                </td>
            </tr>
                {if $id_classe gt 0}
                <tr id='row_grid_classes' class='sfondo_classi' style='display:none;'>
                {else}
                <tr id='row_grid_classes' class='sfondo_classi'>
                {/if}
                <td colspan='2' width='100%'>
                    {mastercom_grid_classes mat_classi=$elenco_classi_accessibili_generale mat_checks=$mat_checks onclick_set_hidden_class_data="1" onclick_submit='yes' default_background='y' onclick_set_hidden_id='id_classe' onclick_set_hidden_name='classe' status_light = 'n' checks_active='no' only_main='y'}
                </td>
                {* //}}} *}
            </tr>
        </table>

        <br>

        {if $errori_mail}
            <div style="color: red; font-weight: bold;">
                {$errori_mail}
            </div>
        {/if}
        {if $errori_sms}
            <div style="color: red; font-weight: bold;">
                {$errori_sms}
            </div>
        {/if}
        {if $conferma_invio}
            <div style="color: green; font-weight: bold;">
                {$conferma_invio}
            </div>
        {/if}

        <table width="100%">
            {if $id_classe > 0}
                <tr>
                    <td colspan="4" class="padding_cella_generica">
                        {if $tipo_utente == 'parente'}
                            <b>{mastercom_label}Genitori della classe{/mastercom_label} {$dati_classe['classe']}{$dati_classe['sezione']} {$dati_classe['descrizione_indirizzi']}</b>
                        {elseif $tipo_utente == 'studente'}
                            <b>{mastercom_label}Studenti della classe{/mastercom_label} {$dati_classe['classe']}{$dati_classe['sezione']} {$dati_classe['descrizione_indirizzi']}</b>
                        {/if}
                    </td>
                    <td align="right" colspan="5" class="padding_cella_generica">
                        <button type="button"
                            title="{mastercom_label}Seleziona tutti gli SMS{/mastercom_label}"
                            onclick="checkByClass('check_sms');"
                            >{mastercom_label}Seleziona tutti gli SMS{/mastercom_label}</button>
                        <button type="button"
                            title="{mastercom_label}Seleziona tutte le MAIL{/mastercom_label}"
                            onclick="checkByClass('check_mail');"
                            >{mastercom_label}Seleziona tutte le MAIL{/mastercom_label}</button>
                        <button type="button"
                                style="margin-left: 10px;"
                            title="{mastercom_label}SMS dei PAGANTI{/mastercom_label}"
                            onclick="checkByClass('check_sms_pagante');"
                            >{mastercom_label}SMS dei PAGANTI{/mastercom_label}</button>
                        <button type="button"
                            title="{mastercom_label}MAIL dei PAGANTI{/mastercom_label}"
                            onclick="checkByClass('check_mail_pagante');"
                            >{mastercom_label}MAIL dei PAGANTI{/mastercom_label}</button>
                        {if $tipo_utente == 'studente'}
                            <button type="button"
                                style="margin-left: 10px;"
                                title="{mastercom_label}SMS degli STUDENTI{/mastercom_label}"
                                onclick="checkByClass('check_sms_studente');"
                            >{mastercom_label}SMS degli STUDENTI{/mastercom_label}</button>
                            <button type="button"
                                title="{mastercom_label}MAIL degli STUDENTI{/mastercom_label}"
                                onclick="checkByClass('check_mail_studente');"
                            >{mastercom_label}MAIL degli STUDENTI{/mastercom_label}</button>
                        {/if}
                    </td>
                </tr>
            {/if}
            {if $tipo_utente == 'parente'}
                <tr align="center" class="sfondo_scuro_generico">
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}N.{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Studente{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Genitore{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Parentela{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Nome utente e Password{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{*{mastercom_label}Pagante{/mastercom_label}*}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Comunicazione{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Cell/Mail{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Data ultima comunicazione{/mastercom_label}</b>
                    </td>
                </tr>
                {foreach $parenti_studenti as $studente}
                    {cycle assign="sfondo_riga" values="sfondo_contrasto_generico, sfondo_base_generico"}
                    {foreach $studente['mat_parenti'] as $key => $parente}
                    <tr align="center" class="{$sfondo_riga}">
                        {if $key == 0}
                            <td rowspan="{$studente['mat_parenti']|@count}" class="padding_cella_generica">
                                {$studente['registro']}
                            </td>
                            <td rowspan="{$studente['mat_parenti']|@count}" class="padding_cella_generica">
                                {$studente['cognome']} {$studente['nome']}
                            </td>
                        {/if}
                        <td class="padding_cella_generica">
                            {$parente['cognome']} {$parente['nome']}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['parentela'] == 'M'}
                                {mastercom_label}Madre{/mastercom_label}
                            {elseif $parente['parentela'] == 'P'}
                                {mastercom_label}Padre{/mastercom_label}
                            {elseif $parente['parentela'] == 'T'}
                                {mastercom_label}Tutore{/mastercom_label}
                            {else}
                            {/if}
                        </td>
                        <td class="padding_cella_generica">
                            <table>
                                <tr>
                                    <td align="right">{mastercom_label}Utente{/mastercom_label}:</td>
                                    <td><input type='text' readonly="readonly" id='parente_{$parente['id_parente']}_utente' name='parente_{$parente['id_parente']}_utente' value='{$parente['utente']}' size='15'></td>
                                    <td rowspan="2" valign="middle">
                                        <input type='button' title='Rigenera nome utente e password' value='<' onclick="genera_credenziali_parente({$parente['id_parente']});
                                                                                                                        $('#mail_{$parente['id_parente']}').attr('disabled', false);
                                                                                                                        $('#sms_{$parente['id_parente']}').attr('disabled', false);
                                                                                                                        $('#mail_{$parente['id_parente']}').attr('checked', true);
                                                                                                                        $('#modificato_{$parente['id_parente']}').val('SI');
                                                                                                                        ">
                                        {mastercom_label}Rigenera utente e password{/mastercom_label}
                                    </td>
                                </tr>
                                <tr>
                                    <td align="right">{mastercom_label}Password{/mastercom_label}:</td>
                                    <td><input type='text' readonly="readonly" id='parente_{$parente['id_parente']}_codice_attivazione' name='parente_{$parente['id_parente']}_codice_attivazione' value='{$parente['codice_attivazione']}' size='15'></td>
                                </tr>
                            </table>
                                <input type="hidden" value="NO" id="modificato_{$parente['id_parente']}" name="modificato[{$parente['id_parente']}]">
                                <input type="hidden" value="{$parente['email']}" name="mail_parenti[{$parente['id_parente']}]">
                                <input type="hidden" value="{$parente['telefono_cellulare']}" name="cellulare_parenti[{$parente['id_parente']}]">
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['pagante'] == 't'}
                                (pagante)
                            {/if}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['sms_valido'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_sms {if $parente['pagante'] == 't'}check_sms_pagante{/if}" id="sms_{$parente['id_parente']}" name="sms[{$parente['id_parente']}]" value="SI"
                                        {if $parente['password_modificata'] == 'SI'}disabled{/if}> SMS
                                </label>
                            {/if}
                            <br>
                            {if $parente['email_valida'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_mail {if $parente['pagante'] == 't'}check_mail_pagante{/if}" id="mail_{$parente['id_parente']}" name="mail[{$parente['id_parente']}]" value="SI"
                                        {if $parente['password_modificata'] == 'SI'}disabled{/if}> MAIL
                                </label>
                            {/if}
                        </td>
                        <td align="left" class="padding_cella_generica">
                            {$parente['telefono_cellulare']}
                            <br>
                            {$parente['email']}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['data_comunicazione_password'] > 0}
                                {$parente['data_comunicazione_password_convertita']}
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                {/foreach}
            {elseif $tipo_utente == 'studente'}
                <tr align="center" class="sfondo_scuro_generico">
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}N.{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Studente{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Utente Quaderno/Moodle e Password{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Genitore{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Parentela{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{*{mastercom_label}Pagante{/mastercom_label}*}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Comunicazione{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Cell/Mail{/mastercom_label}</b>
                    </td>
                    <td class="padding_cella_generica">
                        <b>{mastercom_label}Data ultima comunicazione{/mastercom_label}</b>
                    </td>
                </tr>
                {foreach $parenti_studenti as $studente}
                    {cycle assign="sfondo_riga" values="sfondo_contrasto_generico, sfondo_base_generico"}
                    <tr align="center" class="{$sfondo_riga}">
                        <td rowspan="{$studente['mat_parenti']|@count + 1}" class="padding_cella_generica">
                            {$studente['registro']}
                        </td>
                        <td rowspan="{$studente['mat_parenti']|@count + 1}" class="padding_cella_generica">
                            {$studente['cognome']} {$studente['nome']}
                        </td>
                        <td rowspan="{$studente['mat_parenti']|@count + 1}" class="padding_cella_generica">
                            <table>
                                <tr>
                                    <td>
                                        {mastercom_label}Utente{/mastercom_label}<br>
                                        <input type='text' readonly id='studente_{$studente['id_studente']}_utente' name='studente_{$studente['id_studente']}_utente' value='{$studente['codice_studente']}'>
                                        <input type='button' title='Rigenera codice studente' value='<' onclick="genera_user_studente('studente_{$studente['id_studente']}_utente'); $('#modificato_{$studente['id_studente']}').val('SI');">
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        {mastercom_label}Password{/mastercom_label}<br>
                                        <select name='mantieni_{$studente['id_studente']}' onchange="trigger_cambio_password_massivo(this.value, 'studente_{$studente['id_studente']}_password', 'studente_{$studente['id_studente']}_genera_password');">
                                            <option selected value='SI'>{mastercom_label}Mantieni{/mastercom_label}</option>
                                            <option value='NO'>{mastercom_label}Imposta{/mastercom_label}</option>
                                        </select>
                                        <input type='text' readonly id='studente_{$studente['id_studente']}_password' name='studente_{$studente['id_studente']}_password' value='{$studente['password_studente']}'>
                                        <input type='button' disabled id='studente_{$studente['id_studente']}_genera_password' title='Rigenera password studente' value='<' onclick="genera_pwd_studente('studente_{$studente['id_studente']}_password'); $('#modificato_{$studente['id_studente']}').val('SI');">
                                    </td>
                                </tr>
                            </table>
                            <input type="hidden" value="NO" id="modificato_{$studente['id_studente']}" name="modificato[{$studente['id_studente']}]">
                        </td>
                        <td class="padding_cella_generica">
                            {$studente['cognome']} {$studente['nome']}
                        </td>
                        <td class="padding_cella_generica" colspan="2">
                            {mastercom_label}Studente{/mastercom_label}
                        </td>
                        <td class="padding_cella_generica">
                            {if $studente['cellulare_valido'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_sms check_sms_studente" id="sms_s_{$studente['id_studente']}" name="sms[{$studente['id_studente']}][s_{$studente['id_studente']}]" value="SI"
                                        > SMS
                                </label>
                            {/if}
                            <br>
                            {if $studente['email_valida'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_mail check_mail_studente" id="mail_s_{$studente['id_studente']}" name="mail[{$studente['id_studente']}][s_{$studente['id_studente']}]" value="SI"
                                        > MAIL
                                </label>
                            {/if}
                            <input type="hidden" value="{$studente['email1']}" name="mail_parenti[s_{$studente['id_studente']}]">
                            <input type="hidden" value="{$studente['cellulare_allievo']}" name="cellulare_parenti[s_{$studente['id_studente']}]">
                        </td>
                        <td align="left" class="padding_cella_generica">
                            {$studente['cellulare_allievo']}
                            <br>
                            {$studente['email1']}
                        </td>
                        <td rowspan="{$studente['mat_parenti']|@count + 1}" class="padding_cella_generica">
                            {if $studente['data_comunicazione_password'] > 0}
                                {$studente['data_comunicazione_password_convertita']}
                            {/if}
                        </td>
                    </tr>
                    {foreach $studente['mat_parenti'] as $key => $parente}
                    <tr align="center" class="{$sfondo_riga}">
                        <td class="padding_cella_generica">
                            {$parente['cognome']} {$parente['nome']}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['parentela'] == 'M'}
                                {mastercom_label}Madre{/mastercom_label}
                            {elseif $parente['parentela'] == 'P'}
                                {mastercom_label}Padre{/mastercom_label}
                            {elseif $parente['parentela'] == 'T'}
                                {mastercom_label}Tutore{/mastercom_label}
                            {else}
                            {/if}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['pagante'] == 't'}
                                (pagante)
                            {/if}
                        </td>
                        <td class="padding_cella_generica">
                            {if $parente['sms_valido'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_sms {if $parente['pagante'] == 't'}check_sms_pagante{/if}" id="sms_{$parente['id_parente']}" name="sms[{$studente['id_studente']}][{$parente['id_parente']}]" value="SI"
                                        > SMS
                                </label>
                            {/if}
                            <br>
                            {if $parente['email_valida'] == 'SI'}
                                <label style="cursor: pointer;">
                                    <input type="checkbox" class="check_mail {if $parente['pagante'] == 't'}check_mail_pagante{/if}" id="mail_{$parente['id_parente']}" name="mail[{$studente['id_studente']}][{$parente['id_parente']}]" value="SI"
                                        > MAIL
                                </label>
                            {/if}
                            <input type="hidden" value="{$parente['email']}" name="mail_parenti[{$parente['id_parente']}]">
                            <input type="hidden" value="{$parente['telefono_cellulare']}" name="cellulare_parenti[{$parente['id_parente']}]">
                        </td>
                        <td align="left" class="padding_cella_generica">
                            {$parente['telefono_cellulare']}
                            <br>
                            {$parente['email']}
                        </td>
                    </tr>
                    {/foreach}
                {/foreach}
            {/if}
        </table>

        {if $id_classe > 0}
            <div align="center" style="padding: 20px;">
                <textarea name="messaggio_comunicazione_credenziali_new" cols="40" rows="6" placeholder="{mastercom_label}Messaggio da inserire tra l'intestazione e le credenziali da comunicare{/mastercom_label}"
                          >{$messaggio_comunicazione_credenziali}</textarea>
                <br>
                {if $tipo_invio == 'MC2'}
                    <select name="id_mail">
                            {foreach $mails as $mail}
                                <option value="{$mail['id']}"
                                        {if $mail['fatturapa'] === '1'}selected{/if}
                                        >{$mail['name']}</option>
                            {/foreach}
                    </select>
                    <button type="button"
                            onclick="this.form.operazione.value='{if $tipo_utente == 'parente'}salva_e_invia_credenziali_parenti{elseif $tipo_utente == 'studente'}salva_e_invia_credenziali_studenti{/if}'; this.form.submit();"
                            >{mastercom_label}Salva e Invia{/mastercom_label}</button>
                {elseif $tipo_invio == 'MAILER'}
                    {if $abilita_invio == true}
                        <button type="button"
                            onclick="this.form.operazione.value='{if $tipo_utente == 'parente'}salva_e_invia_credenziali_parenti{elseif $tipo_utente == 'studente'}salva_e_invia_credenziali_studenti{/if}'; this.form.submit();"
                            >{mastercom_label}Salva e Invia{/mastercom_label}</button>
                    {else}
                        {mastercom_label}Mailer non configurato{/mastercom_label}
                    {/if}
                {/if}
                <button type="button"
                        onclick="this.form.submit();"
                        style="margin-left: 20px;"
                        >{mastercom_label}Annulla{/mastercom_label}</button>
            </div>
        {/if}

        <input type='hidden' name='form_stato' value='{$form_stato}'>
        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
        <input type='hidden' name='stato_secondario' value='rigenerazione_comunicazione_password'>
        <input type='hidden' name='current_user' id="current_user" value='{$current_user}'>
        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
        <input type='hidden' name='id_classe' value='{$id_classe}'>
        <input type='hidden' name='classe' value=''>
        <input type='hidden' name='tipo_utente' id="tipo_utente" value='{$tipo_utente}'>
        <input type='hidden' name='operazione' value=''>
        <input type='hidden' name='current_key' id="current_key" value='{$current_key}'>
    </form>
{/if}

{if $stato_secondario != "gestione_operazioni_preliminari_display" and $tipo_classi != 'prossimo anno scolastico'}
</td>
</tr>
{* {{{ Nuovi menu impostazioni*}
<tr><td><br></td></tr>
<tr>
    <td valign='top' width='100%' align='center'>
        <form method='post' action='{$SCRIPT_NAME}'>
            <table width='100%'>
                <tr>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}A - Impostazioni di base{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=20 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='A' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}B - Operazioni di manutenzione{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=20 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='B' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}C - Gestione operativa{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=20 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='C' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}D - Scrutini e esami di stato{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=8 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='D' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}E - Orario e controllo accessi{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=8 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='E' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td align='center'class="setup_contenitore">
                        <table width='100%'>
                            <tr>
                                <td align='center' class="setup_titolo">
                                    {mastercom_label}F - Comunicazioni{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align='center'>
                                    {mastercom_permission_select name="tipo_stampa" size=8 ondblclick="this.form.stato_secondario.value=this.value; this.form.submit();" lista='F' tipo_lista='setup' elenco=$elenco_funzioni privilegi=$privilegi superutente_int=$superutente_int style="width:100%"}
                                    {/mastercom_permission_select}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>

            <input type='hidden' name='form_stato' value='{$form_stato}'>
            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
            <input type='hidden' name='stato_secondario' value=''>
            <input type='hidden' name='stato_recupero' value='seleziona_anno'>
            <input type='hidden' name='current_user' value='{$current_user}'>
            <input type='hidden' name='current_key' value='{$current_key}'>
        </form>
    </td>
</tr>
{* }}} *}
</table>
{foreach from=$anni_per_parametri item=anno_storico}
<div id="messaggio_bocciati_{$anno_storico|replace:"mastercom_":""}" title="Messaggio per gli studenti bocciati {$anno_storico|replace:"mastercom_":""|replace:"_":" "}" style="font-size:80%">
Inserire un messaggio<br><br>
<input type="text"
       name="popup[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_BOCCIATI_PORTALE.nome}]"
       size="80">
</div>
{/foreach}
{foreach from=$anni_per_parametri item=anno_storico}
<div id="messaggio_sospesi_{$anno_storico|replace:"mastercom_":""}" title="Messaggio per gli studenti sospesi {$anno_storico|replace:"mastercom_":""|replace:"_":" "}" style="font-size:80%">
Inserire un messaggio<br><br>
<input type="text"
       name="popup[{$anno_storico|replace:"mastercom_":""}][{$elenco_parametri.$anno_storico.MESSAGGIO_GIUDIZIO_SOSPESI_PORTALE.nome}]"
       size="80">
</div>
{/foreach}
{include file="footer_amministratore.tpl"}

{/if}
