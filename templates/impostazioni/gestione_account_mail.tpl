<script type="text/javascript" language="javascript" src="javascript/microsoft.js?v={$js_version}"></script>
<script type="text/javascript">
    function configuraAccountGoogle(){
        $('#operazione').val('configura_account_google');
        $('#form_container').attr('target','_blank').submit().removeAttr('target');
        $('#operazione').val('');
    }

    function aggiornaAccount(protocollo, idAccount){
        switch (protocollo){
            case 'gmail':
                aggiornaAccountGoogle(idAccount);
                break;
            case 'microsoft-graph':
                aggiornaAccountMicrosoft(idAccount);
                break;
            default:
                break;
        }
    }

    function aggiornaAccountMicrosoft(idAccount){
        $("#id_account").val(idAccount);
        getMailConsent('aggiorna_account_microsoft');
    }

    function aggiornaAccountGoogle(idAccount){
        $('#operazione').val('aggiorna_account_google');
        $("#id_account").val(idAccount);
        $('#form_container').attr('target','_blank').submit().removeAttr('target');
        $('#operazione').val('');
    }

    function aggiornaTipoInvioMail(){
        $('#operazione').val('aggiorna_tipo_invio_email');
        $('#form_container').submit();
    }

    function eliminaAccount(idAccount, totalMails){
        if (totalMails > 0){
            alert("Impossibile eliminare l'account in quanto sono state inviate mail");
        } else {
            if (confirm("Eliminare l'account?")){
                $("#id_account").val(idAccount);
                $("#operazione").val('elimina_account');
                $('#form_container').submit();
            }
        }
    }

    function modificaAccount(idAccount){
        $("#id_account").val(idAccount);
        $("#operazione").val('modifica_account');
        $('#form_container').submit();
    }

    $(document).ready( function(){
        $('#message').delay(5000).fadeOut();
    });

</script>

{if $messaggio}
    <div id="message" class="messaggio_basso_scomparsa" style="font-size: 20pt">
        {$messaggio}
    </div>
{/if}

<form method='post' action='{$SCRIPT_NAME}' id="form_container">
    <div class="div_scheda_generica">
        {if $stato_secondario == 'gestione_account_mail_mc2'}
            <div class="padding6">
                <h1>{mastercom_label}Gestione account mail MC2{/mastercom_label}</h1>
                <div class="padding8" align="center">
                    <button type="button"
                        class="btn_pieno sfondo_azzurro testo_bianco ombra_testo ripples margin8"
                        onclick="$('#stato_secondario').val('gestione_account_mail'); $('#form_container').submit();"
                    >{mastercom_label}Ritorna a gestione account mail{/mastercom_label}</button>
                </div>
            </div>
        {else}
            {if $superutente_int == 'SI' }
            <div class="padding6">
                <h1>{mastercom_label}Gestione account mail{/mastercom_label}</h1>
                <div class='scheda_interna bordo_grigio_chiaro margin-top8 '>
                    <div class='scheda_interna bordo_grigio_chiaro margin-top8 '>
                        <div class="div_testo_titolo_scheda_sx">
                            {mastercom_label}Configura nuovo account{/mastercom_label}
                        </div>
                        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
                            <div class="margin-top8 padding6">
                                <table>
                                    <tr>
                                        <td class="padding_cella_generica">{mastercom_label}Tags{/mastercom_label}</td>
                                        <td class="padding_cella_generica">
                                            {foreach $elenco_tag as $tag}
                                                <label>
                                                <input type="checkbox" value="{$tag}" name="tags_nuovo_account[]" {if $tag == 'default'}checked{/if}>{$tag}</label>
                                            {/foreach}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="padding_cella_generica">{mastercom_label}Destinatari per messaggio{/mastercom_label}</td>
                                        <td class="padding_cella_generica">
                                            <input type="number" name="recipients_per_message" value="100" style="width: 70px;"> /100
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="padding_cella_generica">{mastercom_label}Mail per giorno{/mastercom_label}</td>
                                        <td class="padding_cella_generica">
                                            <input type="number" name="mails_per_day" value="2000" style="width: 70px;"> /2000
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="padding_cella_generica">{mastercom_label}Totale destinatari per giorno{/mastercom_label}</td>
                                        <td class="padding_cella_generica">
                                            <input type="number" name="total_recipients_per_day" value="3000" style="width: 70px;"> /3000
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="padding_cella_generica">{mastercom_label}Destinatari unici per giorno{/mastercom_label}</td>
                                        <td class="padding_cella_generica">
                                            <input type="number" name="unique_recipients_per_day" value="2000" style="width: 70px;"> /2000
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="margin8 padding6">
                                <div class="btn_pieno sfondo_bianco testo_nero"
                                    style="display: flex; align-items: center;"
                                    onclick="configuraAccountGoogle();">
                                    <svg version="1.1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="28px"
                                        height="18px"
                                        viewBox="10 0 48 48"
                                        class="abcRioButtonSvg">
                                        <g>
                                            <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path>
                                            <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path>
                                            <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"></path>
                                            <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path>
                                            <path fill="none" d="M0 0h48v48H0z"></path>
                                        </g>
                                    </svg>
                                    Google
                                </div>
                            </div>
                            <div>
                                <div class="wpo365-mssignin-wrapper">
                                    <div class="wpo365-mssignin-spacearound">
                                        <div class="wpo365-mssignin-button" onclick="getMailConsent()">
                                            <div class="wpo365-mssignin-logo">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21">
                                                        <title>MS-SymbolLockup</title>
                                                        <rect x="1" y="1" width="9" height="9" fill="#f25022"/>
                                                        <rect x="1" y="11" width="9" height="9" fill="#00a4ef"/>
                                                        <rect x="11" y="1" width="9" height="9" fill="#7fba00"/>
                                                        <rect x="11" y="11" width="9" height="9" fill="#ffb900"/>
                                                </svg>
                                            </div>
                                            <div class="wpo365-mssignin-label">Microsoft</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/if}
            <div class="margin8" align="center">
                {if $superutente_int == 'SI' }
                <button type="button"
                        class="btn_pieno sfondo_azzurro testo_bianco ombra_testo ripples margin8"
                        onclick="$('#form_container').submit();"
                    >{mastercom_label}Aggiorna{/mastercom_label}</button>
                <button type="button"
                        class="btn_flat testo_azzurro bordo_azzurro margin8"
                        onclick="window.open('{$url_mailer}');"
                    >{mastercom_label}Apri mailer{/mastercom_label}</button>
                <div class="inline margin-left32 btn_padding_ridotto bordo_verde bordrad5">
                    <select name="parametro_invio_email">
                        <option value="MC2" {if $param_invio_email == 'MC2'}selected{/if}>MC2</option>
                        <option value="MAILER" {if $param_invio_email == 'MAILER'}selected{/if}>MAILER</option>
                    </select>
                    <button type="button"
                        class="btn_flat btn_padding_ridotto sfondo_verde testo_bianco ombra_testo ripples margin6"
                        onclick="aggiornaTipoInvioMail();"
                    >{mastercom_label}Salva{/mastercom_label}</button>
                </div>
                <br>
                {$password_mailer}
                {/if}

                {if $mailer_private == 'SI'}
                    <div class="padding8">
                        <button type="button"
                            class="btn_pieno sfondo_azzurro testo_bianco ombra_testo ripples margin8"
                            onclick="$('#stato_secondario').val('gestione_account_mail_mc2'); $('#form_container').submit();"
                        >{mastercom_label}Configurazione account MC2{/mastercom_label}</button>
                    </div>
                {/if}

                {if $lista_account|@count > 0 }
                    <div class="div_testo_titolo_scheda_sx">
                        {mastercom_label}Lista account{/mastercom_label}
                    </div>
                    <table>
                        <tr class="bordo_basso_generico">
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Account{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Email{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Protocollo{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Abilitato{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Tags{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold bordo_destro_generico">{mastercom_label}Impostazioni{/mastercom_label}</td>
                            <td align="center" class="padding_cella_generica bold"></td>
                        </tr>
                        {foreach $lista_account as $account}
                            <tr class="bordo_basso_generico {if $account['enabled'] == 0}sfondo_grigio_chiaro{/if}">
                                <td align="center" class="padding_cella_generica bordo_destro_generico">{$account['id']}</td>
                                <td align="center" class="padding_cella_generica bordo_destro_generico">{$account['email']}</td>
                                <td align="center" class="padding_cella_generica bordo_destro_generico">
                                    {$account['protocol']}
                                    <input type="hidden" name="protocol[{$account['id']}]" value="{$account['protocol']}">
                                </td>
                                <td align="left" class="padding_cella_generica bordo_destro_generico">
                                    <label>
                                    {if $superutente_int == 'SI' }
                                    <input type="radio" name="modifica_account[{$account['id']}][enabled]"
                                        value="1"
                                        {if $account['enabled'] == 1}checked{/if}
                                        >
                                    {mastercom_label}SI{/mastercom_label}</label>
                                    <label>
                                    <br>
                                    <input type="radio" name="modifica_account[{$account['id']}][enabled]"
                                        value="0"
                                        {if $account['enabled'] == 0}checked{/if}
                                        >{mastercom_label}NO{/mastercom_label}</label>
                                    {else}
                                        {if $account['enabled'] == 1}
                                            {mastercom_label}SI{/mastercom_label}
                                        {else}
                                            {mastercom_label}NO{/mastercom_label}
                                        {/if}
                                    {/if}
                                </td>
                                <td align="center" class="padding_cella_generica bordo_destro_generico">
                                    {foreach $elenco_tag as $tag}
                                        <label>
                                        {if $superutente_int == 'SI' }
                                            <input type="checkbox" value="{$tag}" name="tags[{$account['id']}][]"
                                                {foreach $account['tags'] as $tag_account}
                                                    {if $tag_account == $tag}
                                                        checked
                                                    {/if}
                                                {/foreach}
                                            >{$tag}
                                        {else}
                                            {foreach $account['tags'] as $tag_account}
                                                {if $tag_account == $tag}
                                                    {$tag}
                                                {/if}
                                            {/foreach}
                                        {/if}
                                        </label>
                                    {/foreach}
                                </td>
                                <td align="center" class="padding_cella_generica bordo_destro_generico">
                                    <table>
                                        <tr>
                                            <td class="padding_cella_generica">{mastercom_label}Destinatari per messaggio{/mastercom_label}</td>
                                            <td class="padding_cella_generica">
                                                {if $superutente_int == 'SI' }
                                                <input type="number" name="modifica_account[{$account['id']}][recipients_per_message]" value="{$account['recipients_per_message']}" style="width: 70px;"> /100
                                                {else}
                                                    {$account['recipients_per_message']}/100
                                                {/if}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="padding_cella_generica">{mastercom_label}Mail per giorno{/mastercom_label}</td>
                                            <td class="padding_cella_generica">
                                                {if $superutente_int == 'SI' }
                                                <input type="number" name="modifica_account[{$account['id']}][mails_per_day]" value="{$account['mails_per_day']}" style="width: 70px;"> /2000
                                                {else}
                                                    {$account['mails_per_day']}/2000
                                                {/if}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="padding_cella_generica">{mastercom_label}Totale destinatari per giorno{/mastercom_label}</td>
                                            <td class="padding_cella_generica">
                                                {if $superutente_int == 'SI' }
                                                <input type="number" name="modifica_account[{$account['id']}][total_recipients_per_day]" value="{$account['total_recipients_per_day']}" style="width: 70px;"> /3000
                                                {else}
                                                    {$account['total_recipients_per_day']}/3000
                                                {/if}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="padding_cella_generica">{mastercom_label}Destinatari unici per giorno{/mastercom_label}</td>
                                            <td class="padding_cella_generica">
                                                {if $superutente_int == 'SI' }
                                                <input type="number" name="modifica_account[{$account['id']}][unique_recipients_per_day]" value="{$account['unique_recipients_per_day']}" style="width: 70px;"> /2000
                                                {else}
                                                    {$account['unique_recipients_per_day']}/2000
                                                {/if}
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td align="center" class="padding_cella_generica">
                                    {if $superutente_int == 'SI' }
                                    <button type="button"
                                        class="btn_flat btn_padding_ridotto sfondo_verde testo_bianco ombra_testo ripples margin6"
                                        onclick="modificaAccount('{$account['id']}');"
                                    >{mastercom_label}Salva{/mastercom_label}</button>
                                    {/if}<br>
                                    {if $superutente_int == 'SI' OR $mailer_private == 'SI'}
                                    <button type="button"
                                        class="btn_flat btn_padding_ridotto sfondo_arancio testo_bianco ombra_testo ripples margin6"
                                        onclick="aggiornaAccount('{$account['protocol']}', '{$account['id']}');"
                                    >{mastercom_label}Aggiorna password{/mastercom_label}</button>
                                    {/if}<br>
                                    {if $superutente_int == 'SI' }
                                    <button type="button"
                                        class="btn_flat btn_padding_ridotto {if $account['total_mails'] > 0}sfondo_grigio_scuro{else}sfondo_rosso{/if} testo_bianco ombra_testo ripples margin6"
                                        onclick="eliminaAccount('{$account['id']}', {$account['total_mails']});"
                                    >{mastercom_label}Elimina{/mastercom_label}</button><br>
                                    {/if}
                                </td>
                            </tr>
                        {/foreach}
                    </table>
                {/if}
            </div>
        {/if}
    </div>

    <div id="toastsContainer" style="position: fixed; z-index: 6; bottom: 0px; left: 0px; padding: 10px;"></div>

    <input type='hidden' name='form_stato' value='{$form_stato}'>
    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
    <input type='hidden' name='stato_secondario' id='stato_secondario' value='{$stato_secondario}'>
    <input type='hidden' name='operazione' id='operazione' value=''>
    <input type='hidden' name='id_account' id='id_account' value=''>
    <input type='hidden' name='mcode' id='mcode' value=''>
    <input type='hidden' name='mstate' id='mstate' value=''>
    <input type='hidden' name='mscopes' id='mscopes' value=''>
    <input type='hidden' name='current_user' id='current_user' value='{$current_user}'>
    <input type='hidden' name='current_key' id='current_key' value='{$current_key}'>
    <input type='hidden' id='tipo_utente' value='{$form_stato}'>
</form>