<?php
require_once '/var/www-source/next-api/v1/assets/google-api/vendor/autoload.php';
require __DIR__ . '/configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . "/class/NEXUS_MASTERCOM.class.php";
require MC_PATH . "/common/dbconnect.php";
require MC_PATH . "/class/NEXTAPI.inc.php";

session_start();

$client = new Google_Client();
$client->setAuthConfigFile(estrai_parametro_parametri('client_credentials_file_google'));
$client->setRedirectUri('https://' . $_SERVER['HTTP_HOST'] . '/mastercom/oauth2callback.php');
$client->setIncludeGrantedScopes(true);
$client->setAccessType("offline");
$client->setApprovalPrompt("force");

// TODO: Chiedere gli scopes in base agli optionals abilitati
if (isset($_SESSION['target']) && in_array($_SESSION['target'], ['amministratore', 'professore', 'commissario'])){
    // Calendar
    $client->addScope(Google_Service_Calendar::CALENDAR_EVENTS);

    // Drive
    //$client->addScope(Google_Service_Drive::DRIVE);
     
    // Classroom
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSES);
    $client->addScope(Google_Service_Classroom::CLASSROOM_ROSTERS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_ANNOUNCEMENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORK_ME);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORK_STUDENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORKMATERIALS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_TOPICS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PROFILE_EMAILS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_GUARDIANLINKS_ME_READONLY);
    $client->addScope(Google_Service_Classroom::CLASSROOM_GUARDIANLINKS_STUDENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PUSH_NOTIFICATIONS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PROFILE_PHOTOS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_STUDENT_SUBMISSIONS_ME_READONLY);
    $client->addScope(Google_Service_Classroom::CLASSROOM_STUDENT_SUBMISSIONS_STUDENTS_READONLY);
} elseif ($_SESSION['target'] == 'mailer_account'){
    // Gmail
    // $client->addScope(Google_Service_Gmail::MAIL_GOOGLE_COM); //accesso full

    $client->addScope(Google_Service_Gmail::GMAIL_SEND); //Send messages only. No read or modify privileges on mailbox
    $client->addScope(Google_Service_Gmail::GMAIL_MODIFY); //All read/write operations except immediate, permanent deletion of threads and messages, bypassing Trash
    $client->addScope(Google_Service_Gmail::GMAIL_SETTINGS_BASIC); //Manage basic mail settings
    
    //$client->addScope(Google_Service_Gmail::GMAIL_SETTINGS_SHARING); //Manage sensitive mail settings, including forwarding rules and aliases. Note:Operations guarded by this scope are restricted to administrative use only. They are only available to Google Workspace customers using a service account with domain-wide delegation
} else {
    // Calendar
    $client->addScope(Google_Service_Calendar::CALENDAR_EVENTS_READONLY);

    // Drive
    //$client->addScope(Google_Service_Drive::DRIVE);

    // Classroom
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSES);
    $client->addScope(Google_Service_Classroom::CLASSROOM_ROSTERS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_ANNOUNCEMENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORK_ME);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORK_STUDENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_COURSEWORKMATERIALS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_TOPICS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PROFILE_EMAILS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_GUARDIANLINKS_ME_READONLY);
    $client->addScope(Google_Service_Classroom::CLASSROOM_GUARDIANLINKS_STUDENTS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PUSH_NOTIFICATIONS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_PROFILE_PHOTOS);
    $client->addScope(Google_Service_Classroom::CLASSROOM_STUDENT_SUBMISSIONS_ME_READONLY);
    $client->addScope(Google_Service_Classroom::CLASSROOM_STUDENT_SUBMISSIONS_STUDENTS_READONLY);


}

$service = new Google_Service_Oauth2($client); //serve per fargli ritornare l'id_token
$client->addScope(Google_Service_Plus::USERINFO_EMAIL); //serve per fargli ritornare l'id_token

if (!isset($_GET['code'])) {
    $auth_url = $client->createAuthUrl();
    header('Location: ' . filter_var($auth_url, FILTER_SANITIZE_URL));
} else {
    $client->authenticate($_GET['code']);

    if ($_SESSION['target'] == 'mailer_account') {
        $access_token = $client->getAccessToken();
        $client_info = $client->verifyIdToken();
        $email = $client_info['email'];

        $utenza = estrai_utenza_api_email();
        $autenticazione = [
            'username'    =>    $utenza['username'],
            'password'    =>    $utenza['password']
        ];
        
        // id -> univoco
        // username -> mail dell'account
        // enabled -> true/false
        // protocol -> 'gmail'
        // Recipients per message -> quanti destinatari puo' avere un messaggio (100)
        // Mails per day -> mail giornaliere
        // Total recipients per day -> destinatari al giorno (100)
        // Unique recipients per day -> destinatari univoci per mail
        // Tags -> ('default' in almeno uno, ...)
        // Config -> file client_credential_2.json
        // Credentials -> json return del token google

        if (strlen($_SESSION['id_account']) > 0){
            $response_json = chiamata_api($utenza['url'] . '/api/account/' . $_SESSION['id_account'] . '/', [], 'GET', $autenticazione);
            $account = json_decode($response_json, true);

            if ($account['email'] == $email){
                $payload = [
                    "protocol"      => 'gmail',
                    "config"        =>  file_get_contents(estrai_parametro_parametri('client_credentials_file_google')),
                    "credentials"   =>  json_encode($access_token)
                ];
                $response_json = chiamata_api($utenza['url'] . '/api/account/' . $_SESSION['id_account'] . '/', $payload, 'PATCH', $autenticazione);
                $response = json_decode($response_json, true);
            }
        } else {
            $payload = [
                "id"    =>  $email,
                // "id"    =>  uniqid(preg_replace ('/[^\p{L}\p{N}]/u', '', $email) . '_'),
                // "username"  =>  $email,
                "email"  =>  $email,
                // "enabled"   =>  true,
                "protocol"  => 'gmail',
                "recipients_per_message"    =>  $_SESSION['post']['recipients_per_message'],
                "mails_per_day"             =>  $_SESSION['post']['mails_per_day'],
                "total_recipients_per_day"  =>  $_SESSION['post']['total_recipients_per_day'],
                "unique_recipients_per_day" =>  $_SESSION['post']['unique_recipients_per_day'],
                "tags"                      =>  $_SESSION['post']['tags_nuovo_account'],
                "config"                    =>  file_get_contents(estrai_parametro_parametri('client_credentials_file_google')),
                "credentials"               =>  json_encode($access_token)
            ];

            $response_json = chiamata_api($utenza['url'] . '/api/account/', $payload, 'POST', $autenticazione);
            $response = json_decode($response_json, true);
        }


        //file_put_contents('/tmp/mailer_google_token', date('Y-m-d H:i') . "\n\n");
        //file_put_contents('/tmp/mailer_google_token', print_r("\naccess_token\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($access_token, true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r("\nclient_info\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($client_info, true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r("\npayload\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($payload, true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r("\nutenza\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($utenza, true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r("\nresponse\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($response, true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r("\naccount\n\n", true), FILE_APPEND);
        //file_put_contents('/tmp/mailer_google_token', print_r($account, true), FILE_APPEND);
    } else {
        $user_info = nextapi_call('login', 'POST', null, $_SESSION['current_key']);
        $client_info = $client->verifyIdToken();

        if (strtolower($user_info['email']) == strtolower($client_info['email'])){
            // account autorizzato corretto 
            nextapi_call('user/update_gOfflineAccess', 'POST', ['gOfflineAccess' => $client->getAccessToken(), 'requestDate' => time()], $_SESSION['current_key']);
        } else {
            // account autorizzato sbagliato
        }
    }

    $redirect_page = (isset($_SESSION['redirect_page']) && strlen($_SESSION['redirect_page']) > 0) ? $_SESSION['redirect_page'] : "";
    $redirect_uri = 'https://' . $_SERVER['HTTP_HOST'] . '/' . $redirect_page;
    header('Location: ' . filter_var($redirect_uri, FILTER_SANITIZE_URL));
}